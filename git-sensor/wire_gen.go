// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/devtron-labs/common-lib/monitoring"
	"github.com/devtron-labs/common-lib/pubsub-lib"
	"github.com/devtron-labs/git-sensor/api"
	"github.com/devtron-labs/git-sensor/app"
	"github.com/devtron-labs/git-sensor/internals"
	"github.com/devtron-labs/git-sensor/internals/logger"
	"github.com/devtron-labs/git-sensor/internals/sql"
	"github.com/devtron-labs/git-sensor/pkg"
	"github.com/devtron-labs/git-sensor/pkg/git"
)

// Injectors from wire.go:

func InitializeApp() (*app.App, error) {
	sugaredLogger := logger.NewSugaredLogger()
	config, err := sql.GetConfig()
	if err != nil {
		return nil, err
	}
	db, err := sql.NewDbConnection(config, sugaredLogger)
	if err != nil {
		return nil, err
	}
	materialRepositoryImpl := sql.NewMaterialRepositoryImpl(db)
	configuration, err := internals.ParseConfiguration()
	if err != nil {
		return nil, err
	}
	gitManagerImpl := git.NewGitManagerImpl(sugaredLogger, configuration)
	repositoryManagerImpl := git.NewRepositoryManagerImpl(sugaredLogger, configuration, gitManagerImpl)
	repositoryManagerAnalyticsImpl := git.NewRepositoryManagerAnalyticsImpl(repositoryManagerImpl, gitManagerImpl, configuration, sugaredLogger)
	gitProviderRepositoryImpl := sql.NewGitProviderRepositoryImpl(db)
	ciPipelineMaterialRepositoryImpl := sql.NewCiPipelineMaterialRepositoryImpl(db, sugaredLogger)
	repositoryLocker := internals.NewRepositoryLocker(sugaredLogger)
	pubSubClientServiceImpl, err := pubsub_lib.NewPubSubClientServiceImpl(sugaredLogger)
	if err != nil {
		return nil, err
	}
	webhookEventRepositoryImpl := sql.NewWebhookEventRepositoryImpl(db)
	webhookEventParsedDataRepositoryImpl := sql.NewWebhookEventParsedDataRepositoryImpl(db)
	webhookEventDataMappingRepositoryImpl := sql.NewWebhookEventDataMappingRepositoryImpl(db)
	webhookEventDataMappingFilterResultRepositoryImpl := sql.NewWebhookEventDataMappingFilterResultRepositoryImpl(db)
	webhookEventBeanConverterImpl := git.NewWebhookEventBeanConverterImpl()
	webhookEventServiceImpl := git.NewWebhookEventServiceImpl(sugaredLogger, webhookEventRepositoryImpl, webhookEventParsedDataRepositoryImpl, webhookEventDataMappingRepositoryImpl, webhookEventDataMappingFilterResultRepositoryImpl, materialRepositoryImpl, pubSubClientServiceImpl, webhookEventBeanConverterImpl)
	webhookEventParserImpl := git.NewWebhookEventParserImpl(sugaredLogger)
	webhookHandlerImpl := git.NewWebhookHandlerImpl(sugaredLogger, webhookEventServiceImpl, webhookEventParserImpl)
	gitWatcherImpl, err := git.NewGitWatcherImpl(repositoryManagerImpl, materialRepositoryImpl, sugaredLogger, ciPipelineMaterialRepositoryImpl, repositoryLocker, pubSubClientServiceImpl, webhookHandlerImpl, configuration, gitManagerImpl)
	if err != nil {
		return nil, err
	}
	repoManagerImpl := pkg.NewRepoManagerImpl(sugaredLogger, materialRepositoryImpl, repositoryManagerImpl, repositoryManagerAnalyticsImpl, gitProviderRepositoryImpl, ciPipelineMaterialRepositoryImpl, repositoryLocker, gitWatcherImpl, webhookEventRepositoryImpl, webhookEventParsedDataRepositoryImpl, webhookEventDataMappingRepositoryImpl, webhookEventBeanConverterImpl, configuration, gitManagerImpl)
	restHandlerImpl := api.NewRestHandlerImpl(repoManagerImpl, sugaredLogger)
	monitoringRouter := monitoring.NewMonitoringRouter(sugaredLogger)
	muxRouter := api.NewMuxRouter(sugaredLogger, restHandlerImpl, monitoringRouter)
	grpcHandlerImpl := api.NewGrpcHandlerImpl(repoManagerImpl, sugaredLogger)
	appApp := app.NewApp(muxRouter, sugaredLogger, gitWatcherImpl, db, pubSubClientServiceImpl, grpcHandlerImpl)
	return appApp, nil
}
