# DORA Metrics Implementation Documentation

## Overview

This document explains how DORA (DevOps Research and Assessment) metrics are implemented and calculated in the Devtron backend system. The implementation tracks four key metrics: Deployment Frequency, Change Failure Rate, Average Lead Time, and Mean Time to Recovery.

## Architecture Overview

The DORA metrics system consists of several key components:

1. **Data Ingestion**: Deployment events are captured via NATS subscription or REST API
2. **Data Storage**: Events are stored in PostgreSQL with three main tables
3. **Metrics Calculation**: Real-time calculation of DORA metrics
4. **API Exposure**: REST endpoints to fetch calculated metrics

## Data Flow

```
Deployment Event → NATS/REST API → IngestionService → Database Tables
                                                    ↓
Git Sensor ────────────────────────────────────────┘
                                                    ↓
GetDeploymentMetrics API → DeploymentMetricService → Calculate DORA Metrics → Response
```

## Core Components

### 1. IngestionService (`lens/pkg/IngestionService.go`)

**Purpose**: Processes deployment events and stores them in the database.

**Key Methods**:
- `ProcessDeploymentEvent()`: Main entry point for processing deployment events
- `saveAppRelease()`: Stores deployment information
- `savePipelineMaterial()`: Links deployments to git commits
- `fetchAndSaveChangesFromGit()`: Calculates lead time from git data

**Process Flow**:
1. Receives deployment event
2. Saves app release record
3. Saves pipeline material (git commit info)
4. Determines release type (RollForward/RollBack/Patch)
5. Marks previous failed deployments
6. Fetches git changes and calculates lead time

### 2. DeploymentMetricService (`lens/pkg/DeploymentMetricService.go`)

**Purpose**: Calculates and returns DORA metrics based on stored data.

**Key Methods**:
- `GetDeploymentMetrics()`: Main API to fetch metrics for a time period
- `populateMetrics()`: Orchestrates metric calculations
- `calculateChangeFailureRateAndRecoveryTime()`: Calculates failure rate and MTTR
- `calculateChangeSize()`: Calculates deployment size metrics

## Database Schema

### AppRelease Table
```sql
CREATE TABLE app_release (
    id SERIAL PRIMARY KEY,
    app_id INTEGER NOT NULL,
    environment_id INTEGER NOT NULL,
    ci_artifact_id INTEGER NOT NULL,
    release_id INTEGER NOT NULL,
    pipeline_override_id INTEGER NOT NULL,
    trigger_time TIMESTAMP NOT NULL,
    release_status INTEGER, -- 0=Success, 1=Failure
    release_type INTEGER,   -- 0=Unknown, 1=RollForward, 2=RollBack, 3=Patch
    process_stage INTEGER,
    created_time TIMESTAMP,
    updated_time TIMESTAMP,
    change_size_line_added INTEGER DEFAULT 0,
    change_size_line_deleted INTEGER DEFAULT 0
);
```

### LeadTime Table
```sql
CREATE TABLE lead_time (
    id SERIAL PRIMARY KEY,
    app_release_id INTEGER REFERENCES app_release(id),
    pipeline_material_id INTEGER,
    commit_hash VARCHAR,
    commit_time TIMESTAMP,
    lead_time INTERVAL -- Duration from commit to deployment
);
```

### PipelineMaterial Table
```sql
CREATE TABLE pipeline_material (
    pipeline_material_id INTEGER,
    commit_hash VARCHAR,
    app_release_id INTEGER REFERENCES app_release(id)
);
```

## DORA Metrics Calculations

### 1. Deployment Frequency

**Definition**: How often deployments occur to production.

**Calculation**: 
- Count of releases in a given time period
- Formula: `Number of Deployments / Time Period`

**Implementation**:
```go
// Fetched using GetReleaseBetween() method
releases, err := impl.appReleaseRepository.GetReleaseBetween(appId, envId, from, to)
// Frequency = len(releases) / time_period_in_days
```

### 2. Change Failure Rate

**Definition**: Percentage of deployments that result in failures requiring immediate remediation.

**Calculation**: 
- Formula: `(Failed Deployments / Total Deployments) × 100`

**Implementation**:
```go
func calculateChangeFailureRateAndRecoveryTime(metrics *Metrics) {
    failed := 0
    success := 0
    
    for _, release := range releases {
        if release.ReleaseStatus == sql.Failure {
            failed++
        }
        if release.ReleaseStatus == sql.Success {
            success++
        }
    }
    
    if success+failed > 0 {
        changeFailureRate = float64(failed) * 100 / float64(failed+success)
    }
}
```

**Failure Detection Logic**:
- When a new deployment occurs, the system checks if previous deployment needs to be marked as failed
- Implemented in `markPreviousTriggerFail()` method

### 3. Average Lead Time

**Definition**: Time from code commit to production deployment.

**Calculation**:
- Formula: `Average(Deployment Time - Oldest Commit Time)`

**Implementation**:
```go
// During ingestion, find oldest commit in the deployment
for _, commit := range gitChanges.Commits {
    if oldestTime.After(commit.Author.Date) {
        oldestTime = commit.Author.Date
        oldest = commit
    }
}

// Calculate lead time
leadTime := &sql.LeadTime{
    AppReleaseId: appRelease.Id,
    CommitTime:   oldest.Committer.Date,
    CommitHash:   oldest.Hash.Long,
    LeadTime:     appRelease.TriggerTime.Sub(oldest.Author.Date),
}
```

**Aggregation**:
```go
// Calculate average across all releases
totalLeadTime := float64(0)
leadTimesCount := 0

for _, release := range releases {
    if release.LeadTime != 0 {
        totalLeadTime += release.LeadTime
        leadTimesCount++
    }
}

if leadTimesCount > 0 {
    averageLeadTime = totalLeadTime / float64(leadTimesCount)
}
```

### 4. Mean Time to Recovery (MTTR)

**Definition**: Average time to recover from a failed deployment.

**Calculation**:
- Formula: `Average(Recovery Time for each failure)`
- Recovery Time = `Time of next successful deployment - Time of failure`

**Implementation**:
```go
func calculateRecoveryTime(releases []*Metric) {
    for i := 0; i < len(releases); i++ {
        if releases[i].ReleaseStatus == sql.Failure {
            // Skip consecutive failures
            if i < len(releases)-1 && releases[i+1].ReleaseStatus == sql.Failure {
                continue
            }
            
            // Find next successful deployment
            for j := i - 1; j >= 0; j-- {
                if releases[j].ReleaseStatus == sql.Success {
                    recoveryTime := releases[j].ReleaseTime.Sub(releases[i].ReleaseTime)
                    releases[i].RecoveryTime = recoveryTime.Minutes()
                    totalRecoveryTime += recoveryTime.Minutes()
                    recoveredCount++
                    break
                }
            }
        }
    }
    
    if recoveredCount > 0 {
        averageRecoveryTime = totalRecoveryTime / float64(recoveredCount)
    }
}
```

## API Endpoints

### Get Deployment Metrics

**Endpoint**: `GET /deployment-metrics`

**Query Parameters**:
- `app_id`: Application ID
- `env_id`: Environment ID  
- `from`: Start date (format: YYYY-MM-DD)
- `to`: End date (format: YYYY-MM-DD)

**Response Structure**:
```json
{
    "series": [
        {
            "release_type": "RollForward",
            "release_status": "Success",
            "release_time": "2024-01-15T10:30:00Z",
            "lead_time": 1440.5,
            "cycle_time": 120.0,
            "recovery_time": 0,
            "commit_hash": "abc123...",
            "deployment_size": 150,
            "change_size_line_added": 75,
            "change_size_line_deleted": 75
        }
    ],
    "average_cycle_time": 95.5,
    "average_lead_time": 1200.3,
    "change_failure_rate": 15.2,
    "average_recovery_time": 45.8,
    "average_deployment_size": 125.0,
    "average_line_added": 62.5,
    "average_line_deleted": 62.5,
    "last_failed_time": "2024-01-10T14:20:00Z",
    "recovery_time_last_failed": 30.5
}
```

## Event Processing

### NATS Subscription

Deployment events are automatically processed via NATS:

```go
// In NatsSubscription.go
callback := func(msg *model.PubSubMsg) {
    deploymentEvent := &pkg.DeploymentEvent{}
    json.Unmarshal([]byte(msg.Data), deploymentEvent)
    
    release, err := ingestionService.ProcessDeploymentEvent(deploymentEvent)
    // Handle result
}
```

### REST API Processing

Manual event processing via REST endpoint:

```go
// In RestHandler.go
func (impl *RestHandlerImpl) ProcessDeploymentEvent(w http.ResponseWriter, r *http.Request) {
    deploymentEvent := &pkg.DeploymentEvent{}
    decoder.Decode(deploymentEvent)
    
    release, err := impl.ingestionService.ProcessDeploymentEvent(deploymentEvent)
    // Return response
}
```

## Key Features

### 1. Real-time Processing
- Deployment events are processed immediately via NATS subscription
- Metrics are calculated on-demand when requested

### 2. Git Integration
- Connects to Git Sensor service to fetch commit details
- Calculates accurate lead times based on actual commit timestamps
- Tracks code changes (lines added/deleted)

### 3. Intelligent Failure Detection
- Automatically marks previous deployments as failed when a new deployment occurs
- Handles consecutive failures correctly in MTTR calculation

### 4. Rollback Handling
- Identifies rollback deployments vs new deployments
- Rollbacks don't fetch git details (optimization)

### 5. Change Size Tracking
- Tracks lines of code added and deleted per deployment
- Calculates average deployment size

## Configuration

### Git Sensor Integration
The system can be configured to use either REST or gRPC for Git Sensor communication:

```go
type GitSensorProtocolConfig struct {
    Protocol string `env:"GIT_SENSOR_PROTOCOL" envDefault:"REST"`
}
```

### Database Configuration
Uses PostgreSQL with go-pg ORM for data persistence.

## Monitoring and Logging

The system includes comprehensive logging for:
- Deployment event processing
- Metric calculations
- Error handling
- Performance monitoring

## Best Practices

1. **Data Retention**: Consider implementing data retention policies for old deployment data
2. **Performance**: Large time ranges may impact performance; consider pagination
3. **Error Handling**: Failed git operations don't block deployment processing
4. **Monitoring**: Monitor NATS subscription health for real-time processing

## Troubleshooting

### Common Issues

1. **Missing Lead Times**: Check Git Sensor connectivity and commit data availability
2. **Incorrect Failure Rates**: Verify deployment status updates are being processed
3. **Performance Issues**: Consider database indexing on frequently queried columns

### Debug Endpoints

- Check deployment events processing via logs
- Verify database records in app_release, lead_time, and pipeline_material tables
- Monitor NATS subscription status

---

*This documentation covers the complete DORA metrics implementation in the Devtron backend system. For specific implementation details, refer to the source code in the `lens` package.*
