/*
 * Copyright (c) 2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package errors

import (
	"google.golang.org/grpc/status"
	"strings"
)

// ConvertHelmErrorToInternalError converts known error message from helm to internal error and also maps it with proper grpc code
func ConvertHelmErrorToInternalError(err error) error {
	genericError := getInternalErrorForGenericErrorTypes(err)
	if genericError != nil {
		return genericError
	}
	var internalError error
	for helmErrMsg, internalErr := range helmErrorInternalErrorMap {
		if strings.Contains(err.<PERSON>rror(), helmErrMsg) {
			internalError = status.New(internalErr.grpcCode, internalErr.errorMsg).Err()
		}
	}
	return internalError
}

// getInternalErrorForGenericErrorTypes returns all those kinds of errors which are generic in nature and also dynamic, make sure to return all generic and dynamic errors from this func. instead of putting them in helmErrorInternalErrorMap
func getInternalErrorForGenericErrorTypes(err error) error {
	/*
		for example:-
			1. if namespace is not found err is:- namespace "ns1" not found,
			2. in case ingress class not found error is of type ingress class: IngressClass.networking.k8s.io "ingress1" not found,
			3. when some resource is forbidden then err can be of many formats one of which is:- Unable to continue with install: could not get information about the resource Ingress "prakash-1-prakash-env3-ingress" in namespace "prakash-ns3": ingresses.networking.k8s.io "prakash-1-prakash-env3-ingress" is forbidden...
			etc..
	*/
	for errorMsg, code := range DynamicErrorMapping {
		if strings.Contains(strings.ToLower(err.Error()), errorMsg) {
			return status.New(code, err.Error()).Err()
		}
	}

	return nil
}
