syntax = "proto3";

import "google/protobuf/timestamp.proto";

option go_package = "github.com/devtron-labs/kubelink/bean/grpc/client";

message ClusterConfig {
  string apiServerUrl = 1;
  string token = 2;
  int32 clusterId = 3;
  string clusterName = 4;
  bool insecureSkipTLSVerify = 5;
  string keyData = 6;
  string certData = 7;
  string caData = 8;
  RemoteConnectionConfig RemoteConnectionConfig = 9;
}

message AppListRequest {
  repeated ClusterConfig clusters = 1;
}


service ApplicationService {
  rpc ListApplications(AppListRequest) returns (stream DeployedAppList){}
  rpc ListFluxApplications(AppListRequest) returns (stream FluxApplicationList){}
  rpc GetAppDetail(AppDetailRequest) returns (AppDetail){}
  rpc GetAppStatus(AppDetailRequest) returns (AppStatus){}
  rpc GetAppStatusV2(AppDetailRequest) returns (AppStatus){}
  rpc Hibernate(HibernateRequest) returns (HibernateResponse){}
  rpc UnHibernate(HibernateRequest) returns (HibernateResponse){}
  rpc GetDeploymentHistory(AppDetailRequest) returns (HelmAppDeploymentHistory){}
  rpc GetValuesYaml(AppDetailRequest) returns (ReleaseInfo){}
  rpc GetDesiredManifest(ObjectRequest) returns (DesiredManifestResponse){}
  rpc UninstallRelease(ReleaseIdentifier) returns (UninstallReleaseResponse){}
  rpc UpgradeRelease(UpgradeReleaseRequest) returns (UpgradeReleaseResponse){}
  rpc GetDeploymentDetail(DeploymentDetailRequest) returns (DeploymentDetailResponse){}
  rpc InstallRelease(InstallReleaseRequest) returns (InstallReleaseResponse){}
  rpc UpgradeReleaseWithChartInfo(InstallReleaseRequest) returns (UpgradeReleaseResponse){}
  rpc IsReleaseInstalled(ReleaseIdentifier) returns (BooleanResponse){}
  rpc RollbackRelease(RollbackReleaseRequest) returns (BooleanResponse){}
  rpc TemplateChart(InstallReleaseRequest) returns (TemplateChartResponse){}
  rpc TemplateChartBulk(BulkInstallReleaseRequest) returns (BulkTemplateChartResponse){}
  rpc TemplateChartAndRetrieveChart(InstallReleaseRequest) returns (TemplateChartResponseWithChart){}
  rpc InstallReleaseWithCustomChart(HelmInstallCustomRequest) returns (HelmInstallCustomResponse) {}
  rpc GetNotes(InstallReleaseRequest) returns(ChartNotesResponse) {}
  rpc UpgradeReleaseWithCustomChart(UpgradeReleaseRequest) returns(UpgradeReleaseResponse){}
  rpc ValidateOCIRegistry(RegistryCredential) returns(OCIRegistryResponse) {}
  rpc PushHelmChartToOCIRegistry(OCIRegistryRequest) returns(OCIRegistryResponse) {}
  rpc GetResourceTreeForExternalResources(ExternalResourceTreeRequest) returns(ResourceTreeResponse){}
  rpc GetFluxAppDetail(FluxAppDetailRequest)returns(FluxAppDetail){}
  rpc GetReleaseDetails(ReleaseIdentifier) returns(DeployedAppDetail){}
  rpc BuildResourceTreeUsingParentObjects(GetResourceTreeRequest) returns(ResourceTreeResponse){}
}

message GetResourceTreeRequest{
  ClusterConfig clusterConfig = 1;
  string namespace = 2;
  string ReleaseName = 3;
  // Map key cannot be float, double, bytes, message, or enum types (map<Gvk, Labels> could be a better option)
  ResourceTreeFilter resourceTreeFilter = 4;
  bool PreferCache = 5;
  bool UseFallBack = 6;
  CacheConfig cacheConfig = 7;
  repeated ObjectIdentifier objectIdentifiers = 8;
}

message CacheConfig {
  string serviceName = 1;
  string namespace= 2;
  string passKey= 3;
  string port= 4;
}

message ExternalResourceTreeRequest{
  ClusterConfig clusterConfig = 1;
  repeated ExternalResourceDetail externalResourceDetail = 2;
  CacheConfig cacheConfig = 3;
  bool PreferCache = 4;
  bool UseFallBack = 5;
}

message ExternalResourceDetail {
  string group = 1;
  string kind = 2;
  string version = 3;
  string name = 4;
  string namespace = 5;
}


message DeployedAppList {
  repeated DeployedAppDetail DeployedAppDetail = 1;
  int32 clusterId = 2;
  string errorMsg = 3;
  bool errored = 4;
}

//---------------flux app list-------

message FluxApplicationList {
  int32 clusterId = 1;
  repeated FluxApplication FluxApplication =2;
  string errorMsg = 3;
  bool errored = 4;
}
message FluxApplication{
  string name=1;
  string healthStatus=2;
  string syncStatus=3;
  EnvironmentDetails environmentDetail = 4;
  string fluxAppDeploymentType=5;
  string helmReleaseNamespace = 6;
}
//---------------flux external app detail-------

message FluxAppDetailRequest{
  ClusterConfig clusterConfig = 1;
  string namespace = 2;
  string name = 3;
  bool IsKustomizeApp =4;
  CacheConfig cacheConfig = 5;
  bool PreferCache = 6;
  bool UseFallBack = 7;
}

message FluxAppDetail{
  FluxApplication fluxApplication=1;
  FluxAppStatusDetail FluxAppStatusDetail = 2;
  ResourceTreeResponse resourceTreeResponse =3;
  string applicationStatus = 4;
  string lastObservedGeneration = 5;
}

message FluxAppStatusDetail{
  string Status = 1;
  string Reason = 2;
  string Message = 3;
}

//---------------flux external app detail ends here-------


message DeployedAppDetail {
  string appId = 1;
  string appName = 2;
  string chartName = 3;
  string chartAvatar = 4;
  EnvironmentDetails environmentDetail = 5;
  google.protobuf.Timestamp LastDeployed = 6;
  string chartVersion = 7;
  string releaseStatus = 8;
  string home = 9;
}

message EnvironmentDetails{
  string clusterName = 1;
  int32 clusterId = 2;
  string namespace = 3;
}

//---------------app detail-------

message AppDetailRequest {
  ClusterConfig clusterConfig = 1;
  string namespace = 2;
  string ReleaseName = 3;
  // Map key cannot be float, double, bytes, message, or enum types (map<Gvk, Labels> could be a better option)
  ResourceTreeFilter resourceTreeFilter = 4;
  bool PreferCache = 5;
  bool UseFallBack = 6;
  CacheConfig cacheConfig = 7;
}

message AppDetail{
  string applicationStatus = 1;
  ReleaseStatus releaseStatus = 2;
  google.protobuf.Timestamp lastDeployed = 6;
  ChartMetadata chartMetadata = 7;
  ResourceTreeResponse resourceTreeResponse = 8;
  EnvironmentDetails environmentDetails = 9;
  bool ReleaseExist = 10;
}

message AppStatus{
  string ApplicationStatus = 1;
  string ReleaseStatus = 2;
  string Description = 3;
  google.protobuf.Timestamp LastDeployed = 4;
}

message ReleaseStatus {
  string status = 1;
  string message = 2;
  string description = 3;
}

message ChartMetadata {
  string chartName = 1;
  string chartVersion = 2;
  string home = 3;
  repeated string sources = 4;
  string description = 5;
  // Contains the rendered templates/NOTES.txt
  string notes = 6;
}

message ResourceTreeResponse {
  repeated ResourceNode nodes = 1;
  repeated .PodMetadata podMetadata = 2;
}

message ResourceNode {
  string group = 1;
  string version = 2;
  string kind = 3;
  string namespace = 4;
  string name = 5;
  string uid = 6;
  repeated ResourceRef parentRefs = 7;
  ResourceNetworkingInfo networkingInfo = 8;
  string resourceVersion = 9;
  HealthStatus health = 10;
  bool isHibernated = 11;
  bool canBeHibernated = 12;
  repeated InfoItem info = 13;
  string createdAt = 14;
  repeated int64 port = 15;
  bool isHook = 16;
  string hookType = 17;
}

message InfoItem {
  string name = 1;
  string value = 2;
}

message HealthStatus {
  string status = 1;
  string message = 2;
}
message ResourceNetworkingInfo {
  map<string, string> labels = 1;
}

message ResourceRef {
  string group = 1;
  string version = 2;
  string kind = 3;
  string namespace = 4;
  string name = 5;
  string uid = 6 ;
}

message PodMetadata {
  string name = 1;
  string uid = 2 ;
  repeated string containers = 3;
  repeated string initContainers = 4;
  bool isNew = 5;
  repeated EphemeralContainerData ephemeralContainers = 6;
}

message EphemeralContainerData {
  string name = 1;
  bool isExternal = 2;
}

//--------hibernate

message HibernateRequest {
  ClusterConfig clusterConfig = 1;
  repeated ObjectIdentifier objectIdentifier = 2;
}

message ObjectIdentifier {
  string group = 1;
  string kind = 2;
  string version = 3;
  string name = 4;
  string namespace = 5;
  map<string, string> annotations = 6; //to move this to a internal object or rename ObjectIdentifier
}

message HibernateStatus{
  ObjectIdentifier targetObject = 1;
  bool success = 2;
  string errorMsg = 3;
}

message HibernateResponse{
  repeated HibernateStatus  status = 1;
}
//------------------- deployment history

message  HelmAppDeploymentDetail {
  ChartMetadata chartMetadata = 1;
  repeated string dockerImages = 2;
  int32 version = 3;
  google.protobuf.Timestamp deployedAt = 4;
  string deployedBy = 5;
  string status = 6;
  string message = 7;
}

message HelmAppDeploymentHistory {
  repeated HelmAppDeploymentDetail deploymentHistory = 1;
}

message ReleaseInfo{
  DeployedAppDetail deployedAppDetail = 1;
  string defaultValues = 2;
  string overrideValues = 3;
  string mergedValues = 4;
  string readme = 5;
  string valuesSchemaJson = 6;
}

message ObjectRequest {
  ClusterConfig clusterConfig = 1;
  ObjectIdentifier objectIdentifier = 2;
  string releaseName = 3;
  string releaseNamespace = 4;
}

message DesiredManifestResponse {
  string manifest = 1;
}

message UninstallReleaseResponse {
  bool success = 1;
}

message ReleaseIdentifier {
  ClusterConfig clusterConfig = 1;
  string releaseName = 2;
  string releaseNamespace = 3;
}

message UpgradeReleaseRequest {
  ReleaseIdentifier releaseIdentifier = 1;
  string valuesYaml = 2;
  int32 historyMax = 3;
  ChartContent chartContent = 4;
  bool RunInCtx = 5;
  string K8sVersion = 6;
}

message UpgradeReleaseResponse {
  bool success = 1;
}

message DeploymentDetailRequest {
  ReleaseIdentifier releaseIdentifier = 1;
  int32 deploymentVersion = 2;
}

message DeploymentDetailResponse {
  string manifest = 1;
  string valuesYaml = 2;
}

message ChartRepository {
  string name = 1;
  string url = 2;
  string username = 3;
  string password = 4;
  bool   allowInsecureConnection = 5;
}

message InstallReleaseRequest {
  ReleaseIdentifier releaseIdentifier = 1;
  string chartName = 2;
  string chartVersion = 3;
  string valuesYaml = 4;
  ChartRepository chartRepository = 5;
  string K8sVersion = 6;
  int32 historyMax = 7;
  RegistryCredential RegistryCredential = 8;
  bool IsOCIRepo = 9;
  int32 installAppVersionHistoryId = 10;
  ChartContent chartContent = 11;
  string appName=12;
}

message  BulkInstallReleaseRequest{
  repeated  InstallReleaseRequest BulkInstallReleaseRequest=1;
}

message InstallReleaseResponse {
  bool success = 1;
}

message BooleanResponse {
  bool result = 1;
}

message RollbackReleaseRequest {
  ReleaseIdentifier releaseIdentifier = 1;
  int32 version = 2;
}

message TemplateChartResponse {
  string generatedManifest = 1;
  string appName=2;
}

message BulkTemplateChartResponse {
  repeated TemplateChartResponse BulkTemplateChartResponse=1;
}

message TemplateChartResponseWithChart {
  TemplateChartResponse templateChartResponse = 1;
  ChartContent chartBytes = 2;
}


message HelmInstallCustomRequest {
  string valuesYaml = 1;
  ChartContent chartContent = 2;
  ReleaseIdentifier releaseIdentifier = 3;
  bool RunInCtx = 4;
  string K8sVersion = 5;
}

message HelmInstallCustomResponse {
  bool success = 1;
}

message ChartContent {
  bytes Content = 1;
}

message Gvk {
  string Group = 1;
  string Version = 2;
  string Kind = 3;
}

message ResourceFilter {
  Gvk gvk = 1;
  ResourceIdentifier resourceIdentifier = 2;
}

message ResourceIdentifier {
  map<string, string> labels = 1;
}

message ResourceTreeFilter {
  ResourceIdentifier globalFilter = 1;
  repeated ResourceFilter resourceFilters = 2;
}

message ChartNotesResponse{
  string notes = 1;
}

message OCIRegistryRequest {
  bytes Chart = 1;
  string ChartName = 2;
  string ChartVersion = 3;
  bool IsInsecure = 4;
  RegistryCredential RegistryCredential = 5;
}

message RegistryCredential {
  string RegistryUrl = 1;
  string Username = 2;
  string Password = 3;
  string AwsRegion = 4;
  string AccessKey = 5;
  string SecretKey = 6;
  string RegistryType = 7;
  string RepoName = 8;
  bool IsPublic = 9;
  RemoteConnectionConfig RemoteConnectionConfig = 10;
  string Connection = 11;
  string RegistryName = 12;
  string RegistryCertificate = 13;
  string CredentialsType = 14;
}

enum RemoteConnectionMethod {
  PROXY = 0;
  SSH = 1;
  DIRECT = 2 ;
}

message ProxyConfig {
  string ProxyUrl = 1;
}

message SSHTunnelConfig {
  string SSHServerAddress = 1;
  string SSHUsername = 2;
  string SSHPassword = 3;
  string SSHAuthKey = 4;
}

message RemoteConnectionConfig {
  RemoteConnectionMethod RemoteConnectionMethod = 1;
  ProxyConfig ProxyConfig = 2;
  SSHTunnelConfig SSHTunnelConfig = 3;
}

message OCIRegistryResponse {
  bool IsLoggedIn = 1;
  OCIRegistryPushResponse PushResult = 2;
}

message OCIRegistryPushResponse {
  string Digest = 1;
  string PushedURL = 2;
}