// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.9.1
// source: grpc/applist.proto

package client

import (
	timestamp "github.com/golang/protobuf/ptypes/timestamp"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RemoteConnectionMethod int32

const (
	RemoteConnectionMethod_PROXY  RemoteConnectionMethod = 0
	RemoteConnectionMethod_SSH    RemoteConnectionMethod = 1
	RemoteConnectionMethod_DIRECT RemoteConnectionMethod = 2
)

// Enum value maps for RemoteConnectionMethod.
var (
	RemoteConnectionMethod_name = map[int32]string{
		0: "PROXY",
		1: "SSH",
		2: "DIRECT",
	}
	RemoteConnectionMethod_value = map[string]int32{
		"PROXY":  0,
		"SSH":    1,
		"DIRECT": 2,
	}
)

func (x RemoteConnectionMethod) Enum() *RemoteConnectionMethod {
	p := new(RemoteConnectionMethod)
	*p = x
	return p
}

func (x RemoteConnectionMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RemoteConnectionMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_grpc_applist_proto_enumTypes[0].Descriptor()
}

func (RemoteConnectionMethod) Type() protoreflect.EnumType {
	return &file_grpc_applist_proto_enumTypes[0]
}

func (x RemoteConnectionMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RemoteConnectionMethod.Descriptor instead.
func (RemoteConnectionMethod) EnumDescriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{0}
}

type ClusterConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApiServerUrl           string                  `protobuf:"bytes,1,opt,name=apiServerUrl,proto3" json:"apiServerUrl,omitempty"`
	Token                  string                  `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	ClusterId              int32                   `protobuf:"varint,3,opt,name=clusterId,proto3" json:"clusterId,omitempty"`
	ClusterName            string                  `protobuf:"bytes,4,opt,name=clusterName,proto3" json:"clusterName,omitempty"`
	InsecureSkipTLSVerify  bool                    `protobuf:"varint,5,opt,name=insecureSkipTLSVerify,proto3" json:"insecureSkipTLSVerify,omitempty"`
	KeyData                string                  `protobuf:"bytes,6,opt,name=keyData,proto3" json:"keyData,omitempty"`
	CertData               string                  `protobuf:"bytes,7,opt,name=certData,proto3" json:"certData,omitempty"`
	CaData                 string                  `protobuf:"bytes,8,opt,name=caData,proto3" json:"caData,omitempty"`
	RemoteConnectionConfig *RemoteConnectionConfig `protobuf:"bytes,9,opt,name=RemoteConnectionConfig,proto3" json:"RemoteConnectionConfig,omitempty"`
}

func (x *ClusterConfig) Reset() {
	*x = ClusterConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClusterConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClusterConfig) ProtoMessage() {}

func (x *ClusterConfig) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClusterConfig.ProtoReflect.Descriptor instead.
func (*ClusterConfig) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{0}
}

func (x *ClusterConfig) GetApiServerUrl() string {
	if x != nil {
		return x.ApiServerUrl
	}
	return ""
}

func (x *ClusterConfig) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *ClusterConfig) GetClusterId() int32 {
	if x != nil {
		return x.ClusterId
	}
	return 0
}

func (x *ClusterConfig) GetClusterName() string {
	if x != nil {
		return x.ClusterName
	}
	return ""
}

func (x *ClusterConfig) GetInsecureSkipTLSVerify() bool {
	if x != nil {
		return x.InsecureSkipTLSVerify
	}
	return false
}

func (x *ClusterConfig) GetKeyData() string {
	if x != nil {
		return x.KeyData
	}
	return ""
}

func (x *ClusterConfig) GetCertData() string {
	if x != nil {
		return x.CertData
	}
	return ""
}

func (x *ClusterConfig) GetCaData() string {
	if x != nil {
		return x.CaData
	}
	return ""
}

func (x *ClusterConfig) GetRemoteConnectionConfig() *RemoteConnectionConfig {
	if x != nil {
		return x.RemoteConnectionConfig
	}
	return nil
}

type AppListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clusters []*ClusterConfig `protobuf:"bytes,1,rep,name=clusters,proto3" json:"clusters,omitempty"`
}

func (x *AppListRequest) Reset() {
	*x = AppListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppListRequest) ProtoMessage() {}

func (x *AppListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppListRequest.ProtoReflect.Descriptor instead.
func (*AppListRequest) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{1}
}

func (x *AppListRequest) GetClusters() []*ClusterConfig {
	if x != nil {
		return x.Clusters
	}
	return nil
}

type GetResourceTreeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterConfig *ClusterConfig `protobuf:"bytes,1,opt,name=clusterConfig,proto3" json:"clusterConfig,omitempty"`
	Namespace     string         `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	ReleaseName   string         `protobuf:"bytes,3,opt,name=ReleaseName,proto3" json:"ReleaseName,omitempty"`
	// Map key cannot be float, double, bytes, message, or enum types (map<Gvk, Labels> could be a better option)
	ResourceTreeFilter *ResourceTreeFilter `protobuf:"bytes,4,opt,name=resourceTreeFilter,proto3" json:"resourceTreeFilter,omitempty"`
	PreferCache        bool                `protobuf:"varint,5,opt,name=PreferCache,proto3" json:"PreferCache,omitempty"`
	UseFallBack        bool                `protobuf:"varint,6,opt,name=UseFallBack,proto3" json:"UseFallBack,omitempty"`
	CacheConfig        *CacheConfig        `protobuf:"bytes,7,opt,name=cacheConfig,proto3" json:"cacheConfig,omitempty"`
	ObjectIdentifiers  []*ObjectIdentifier `protobuf:"bytes,8,rep,name=objectIdentifiers,proto3" json:"objectIdentifiers,omitempty"`
}

func (x *GetResourceTreeRequest) Reset() {
	*x = GetResourceTreeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetResourceTreeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetResourceTreeRequest) ProtoMessage() {}

func (x *GetResourceTreeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetResourceTreeRequest.ProtoReflect.Descriptor instead.
func (*GetResourceTreeRequest) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{2}
}

func (x *GetResourceTreeRequest) GetClusterConfig() *ClusterConfig {
	if x != nil {
		return x.ClusterConfig
	}
	return nil
}

func (x *GetResourceTreeRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *GetResourceTreeRequest) GetReleaseName() string {
	if x != nil {
		return x.ReleaseName
	}
	return ""
}

func (x *GetResourceTreeRequest) GetResourceTreeFilter() *ResourceTreeFilter {
	if x != nil {
		return x.ResourceTreeFilter
	}
	return nil
}

func (x *GetResourceTreeRequest) GetPreferCache() bool {
	if x != nil {
		return x.PreferCache
	}
	return false
}

func (x *GetResourceTreeRequest) GetUseFallBack() bool {
	if x != nil {
		return x.UseFallBack
	}
	return false
}

func (x *GetResourceTreeRequest) GetCacheConfig() *CacheConfig {
	if x != nil {
		return x.CacheConfig
	}
	return nil
}

func (x *GetResourceTreeRequest) GetObjectIdentifiers() []*ObjectIdentifier {
	if x != nil {
		return x.ObjectIdentifiers
	}
	return nil
}

type CacheConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceName string `protobuf:"bytes,1,opt,name=serviceName,proto3" json:"serviceName,omitempty"`
	Namespace   string `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	PassKey     string `protobuf:"bytes,3,opt,name=passKey,proto3" json:"passKey,omitempty"`
	Port        string `protobuf:"bytes,4,opt,name=port,proto3" json:"port,omitempty"`
}

func (x *CacheConfig) Reset() {
	*x = CacheConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CacheConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CacheConfig) ProtoMessage() {}

func (x *CacheConfig) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CacheConfig.ProtoReflect.Descriptor instead.
func (*CacheConfig) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{3}
}

func (x *CacheConfig) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *CacheConfig) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *CacheConfig) GetPassKey() string {
	if x != nil {
		return x.PassKey
	}
	return ""
}

func (x *CacheConfig) GetPort() string {
	if x != nil {
		return x.Port
	}
	return ""
}

type ExternalResourceTreeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterConfig          *ClusterConfig            `protobuf:"bytes,1,opt,name=clusterConfig,proto3" json:"clusterConfig,omitempty"`
	ExternalResourceDetail []*ExternalResourceDetail `protobuf:"bytes,2,rep,name=externalResourceDetail,proto3" json:"externalResourceDetail,omitempty"`
	CacheConfig            *CacheConfig              `protobuf:"bytes,3,opt,name=cacheConfig,proto3" json:"cacheConfig,omitempty"`
	PreferCache            bool                      `protobuf:"varint,4,opt,name=PreferCache,proto3" json:"PreferCache,omitempty"`
	UseFallBack            bool                      `protobuf:"varint,5,opt,name=UseFallBack,proto3" json:"UseFallBack,omitempty"`
}

func (x *ExternalResourceTreeRequest) Reset() {
	*x = ExternalResourceTreeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExternalResourceTreeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalResourceTreeRequest) ProtoMessage() {}

func (x *ExternalResourceTreeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalResourceTreeRequest.ProtoReflect.Descriptor instead.
func (*ExternalResourceTreeRequest) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{4}
}

func (x *ExternalResourceTreeRequest) GetClusterConfig() *ClusterConfig {
	if x != nil {
		return x.ClusterConfig
	}
	return nil
}

func (x *ExternalResourceTreeRequest) GetExternalResourceDetail() []*ExternalResourceDetail {
	if x != nil {
		return x.ExternalResourceDetail
	}
	return nil
}

func (x *ExternalResourceTreeRequest) GetCacheConfig() *CacheConfig {
	if x != nil {
		return x.CacheConfig
	}
	return nil
}

func (x *ExternalResourceTreeRequest) GetPreferCache() bool {
	if x != nil {
		return x.PreferCache
	}
	return false
}

func (x *ExternalResourceTreeRequest) GetUseFallBack() bool {
	if x != nil {
		return x.UseFallBack
	}
	return false
}

type ExternalResourceDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Group     string `protobuf:"bytes,1,opt,name=group,proto3" json:"group,omitempty"`
	Kind      string `protobuf:"bytes,2,opt,name=kind,proto3" json:"kind,omitempty"`
	Version   string `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	Name      string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Namespace string `protobuf:"bytes,5,opt,name=namespace,proto3" json:"namespace,omitempty"`
}

func (x *ExternalResourceDetail) Reset() {
	*x = ExternalResourceDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExternalResourceDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalResourceDetail) ProtoMessage() {}

func (x *ExternalResourceDetail) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalResourceDetail.ProtoReflect.Descriptor instead.
func (*ExternalResourceDetail) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{5}
}

func (x *ExternalResourceDetail) GetGroup() string {
	if x != nil {
		return x.Group
	}
	return ""
}

func (x *ExternalResourceDetail) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

func (x *ExternalResourceDetail) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ExternalResourceDetail) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExternalResourceDetail) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

type DeployedAppList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeployedAppDetail []*DeployedAppDetail `protobuf:"bytes,1,rep,name=DeployedAppDetail,proto3" json:"DeployedAppDetail,omitempty"`
	ClusterId         int32                `protobuf:"varint,2,opt,name=clusterId,proto3" json:"clusterId,omitempty"`
	ErrorMsg          string               `protobuf:"bytes,3,opt,name=errorMsg,proto3" json:"errorMsg,omitempty"`
	Errored           bool                 `protobuf:"varint,4,opt,name=errored,proto3" json:"errored,omitempty"`
}

func (x *DeployedAppList) Reset() {
	*x = DeployedAppList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeployedAppList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeployedAppList) ProtoMessage() {}

func (x *DeployedAppList) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeployedAppList.ProtoReflect.Descriptor instead.
func (*DeployedAppList) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{6}
}

func (x *DeployedAppList) GetDeployedAppDetail() []*DeployedAppDetail {
	if x != nil {
		return x.DeployedAppDetail
	}
	return nil
}

func (x *DeployedAppList) GetClusterId() int32 {
	if x != nil {
		return x.ClusterId
	}
	return 0
}

func (x *DeployedAppList) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

func (x *DeployedAppList) GetErrored() bool {
	if x != nil {
		return x.Errored
	}
	return false
}

type FluxApplicationList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterId       int32              `protobuf:"varint,1,opt,name=clusterId,proto3" json:"clusterId,omitempty"`
	FluxApplication []*FluxApplication `protobuf:"bytes,2,rep,name=FluxApplication,proto3" json:"FluxApplication,omitempty"`
	ErrorMsg        string             `protobuf:"bytes,3,opt,name=errorMsg,proto3" json:"errorMsg,omitempty"`
	Errored         bool               `protobuf:"varint,4,opt,name=errored,proto3" json:"errored,omitempty"`
}

func (x *FluxApplicationList) Reset() {
	*x = FluxApplicationList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FluxApplicationList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FluxApplicationList) ProtoMessage() {}

func (x *FluxApplicationList) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FluxApplicationList.ProtoReflect.Descriptor instead.
func (*FluxApplicationList) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{7}
}

func (x *FluxApplicationList) GetClusterId() int32 {
	if x != nil {
		return x.ClusterId
	}
	return 0
}

func (x *FluxApplicationList) GetFluxApplication() []*FluxApplication {
	if x != nil {
		return x.FluxApplication
	}
	return nil
}

func (x *FluxApplicationList) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

func (x *FluxApplicationList) GetErrored() bool {
	if x != nil {
		return x.Errored
	}
	return false
}

type FluxApplication struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name                  string              `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	HealthStatus          string              `protobuf:"bytes,2,opt,name=healthStatus,proto3" json:"healthStatus,omitempty"`
	SyncStatus            string              `protobuf:"bytes,3,opt,name=syncStatus,proto3" json:"syncStatus,omitempty"`
	EnvironmentDetail     *EnvironmentDetails `protobuf:"bytes,4,opt,name=environmentDetail,proto3" json:"environmentDetail,omitempty"`
	FluxAppDeploymentType string              `protobuf:"bytes,5,opt,name=fluxAppDeploymentType,proto3" json:"fluxAppDeploymentType,omitempty"`
	HelmReleaseNamespace  string              `protobuf:"bytes,6,opt,name=helmReleaseNamespace,proto3" json:"helmReleaseNamespace,omitempty"`
}

func (x *FluxApplication) Reset() {
	*x = FluxApplication{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FluxApplication) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FluxApplication) ProtoMessage() {}

func (x *FluxApplication) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FluxApplication.ProtoReflect.Descriptor instead.
func (*FluxApplication) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{8}
}

func (x *FluxApplication) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FluxApplication) GetHealthStatus() string {
	if x != nil {
		return x.HealthStatus
	}
	return ""
}

func (x *FluxApplication) GetSyncStatus() string {
	if x != nil {
		return x.SyncStatus
	}
	return ""
}

func (x *FluxApplication) GetEnvironmentDetail() *EnvironmentDetails {
	if x != nil {
		return x.EnvironmentDetail
	}
	return nil
}

func (x *FluxApplication) GetFluxAppDeploymentType() string {
	if x != nil {
		return x.FluxAppDeploymentType
	}
	return ""
}

func (x *FluxApplication) GetHelmReleaseNamespace() string {
	if x != nil {
		return x.HelmReleaseNamespace
	}
	return ""
}

type FluxAppDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterConfig  *ClusterConfig `protobuf:"bytes,1,opt,name=clusterConfig,proto3" json:"clusterConfig,omitempty"`
	Namespace      string         `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name           string         `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	IsKustomizeApp bool           `protobuf:"varint,4,opt,name=IsKustomizeApp,proto3" json:"IsKustomizeApp,omitempty"`
	CacheConfig    *CacheConfig   `protobuf:"bytes,5,opt,name=cacheConfig,proto3" json:"cacheConfig,omitempty"`
	PreferCache    bool           `protobuf:"varint,6,opt,name=PreferCache,proto3" json:"PreferCache,omitempty"`
	UseFallBack    bool           `protobuf:"varint,7,opt,name=UseFallBack,proto3" json:"UseFallBack,omitempty"`
}

func (x *FluxAppDetailRequest) Reset() {
	*x = FluxAppDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FluxAppDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FluxAppDetailRequest) ProtoMessage() {}

func (x *FluxAppDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FluxAppDetailRequest.ProtoReflect.Descriptor instead.
func (*FluxAppDetailRequest) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{9}
}

func (x *FluxAppDetailRequest) GetClusterConfig() *ClusterConfig {
	if x != nil {
		return x.ClusterConfig
	}
	return nil
}

func (x *FluxAppDetailRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *FluxAppDetailRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FluxAppDetailRequest) GetIsKustomizeApp() bool {
	if x != nil {
		return x.IsKustomizeApp
	}
	return false
}

func (x *FluxAppDetailRequest) GetCacheConfig() *CacheConfig {
	if x != nil {
		return x.CacheConfig
	}
	return nil
}

func (x *FluxAppDetailRequest) GetPreferCache() bool {
	if x != nil {
		return x.PreferCache
	}
	return false
}

func (x *FluxAppDetailRequest) GetUseFallBack() bool {
	if x != nil {
		return x.UseFallBack
	}
	return false
}

type FluxAppDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FluxApplication        *FluxApplication      `protobuf:"bytes,1,opt,name=fluxApplication,proto3" json:"fluxApplication,omitempty"`
	FluxAppStatusDetail    *FluxAppStatusDetail  `protobuf:"bytes,2,opt,name=FluxAppStatusDetail,proto3" json:"FluxAppStatusDetail,omitempty"`
	ResourceTreeResponse   *ResourceTreeResponse `protobuf:"bytes,3,opt,name=resourceTreeResponse,proto3" json:"resourceTreeResponse,omitempty"`
	ApplicationStatus      string                `protobuf:"bytes,4,opt,name=applicationStatus,proto3" json:"applicationStatus,omitempty"`
	LastObservedGeneration string                `protobuf:"bytes,5,opt,name=lastObservedGeneration,proto3" json:"lastObservedGeneration,omitempty"`
}

func (x *FluxAppDetail) Reset() {
	*x = FluxAppDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FluxAppDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FluxAppDetail) ProtoMessage() {}

func (x *FluxAppDetail) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FluxAppDetail.ProtoReflect.Descriptor instead.
func (*FluxAppDetail) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{10}
}

func (x *FluxAppDetail) GetFluxApplication() *FluxApplication {
	if x != nil {
		return x.FluxApplication
	}
	return nil
}

func (x *FluxAppDetail) GetFluxAppStatusDetail() *FluxAppStatusDetail {
	if x != nil {
		return x.FluxAppStatusDetail
	}
	return nil
}

func (x *FluxAppDetail) GetResourceTreeResponse() *ResourceTreeResponse {
	if x != nil {
		return x.ResourceTreeResponse
	}
	return nil
}

func (x *FluxAppDetail) GetApplicationStatus() string {
	if x != nil {
		return x.ApplicationStatus
	}
	return ""
}

func (x *FluxAppDetail) GetLastObservedGeneration() string {
	if x != nil {
		return x.LastObservedGeneration
	}
	return ""
}

type FluxAppStatusDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  string `protobuf:"bytes,1,opt,name=Status,proto3" json:"Status,omitempty"`
	Reason  string `protobuf:"bytes,2,opt,name=Reason,proto3" json:"Reason,omitempty"`
	Message string `protobuf:"bytes,3,opt,name=Message,proto3" json:"Message,omitempty"`
}

func (x *FluxAppStatusDetail) Reset() {
	*x = FluxAppStatusDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FluxAppStatusDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FluxAppStatusDetail) ProtoMessage() {}

func (x *FluxAppStatusDetail) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FluxAppStatusDetail.ProtoReflect.Descriptor instead.
func (*FluxAppStatusDetail) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{11}
}

func (x *FluxAppStatusDetail) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *FluxAppStatusDetail) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *FluxAppStatusDetail) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DeployedAppDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId             string               `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	AppName           string               `protobuf:"bytes,2,opt,name=appName,proto3" json:"appName,omitempty"`
	ChartName         string               `protobuf:"bytes,3,opt,name=chartName,proto3" json:"chartName,omitempty"`
	ChartAvatar       string               `protobuf:"bytes,4,opt,name=chartAvatar,proto3" json:"chartAvatar,omitempty"`
	EnvironmentDetail *EnvironmentDetails  `protobuf:"bytes,5,opt,name=environmentDetail,proto3" json:"environmentDetail,omitempty"`
	LastDeployed      *timestamp.Timestamp `protobuf:"bytes,6,opt,name=LastDeployed,proto3" json:"LastDeployed,omitempty"`
	ChartVersion      string               `protobuf:"bytes,7,opt,name=chartVersion,proto3" json:"chartVersion,omitempty"`
	ReleaseStatus     string               `protobuf:"bytes,8,opt,name=releaseStatus,proto3" json:"releaseStatus,omitempty"`
	Home              string               `protobuf:"bytes,9,opt,name=home,proto3" json:"home,omitempty"`
}

func (x *DeployedAppDetail) Reset() {
	*x = DeployedAppDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeployedAppDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeployedAppDetail) ProtoMessage() {}

func (x *DeployedAppDetail) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeployedAppDetail.ProtoReflect.Descriptor instead.
func (*DeployedAppDetail) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{12}
}

func (x *DeployedAppDetail) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *DeployedAppDetail) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *DeployedAppDetail) GetChartName() string {
	if x != nil {
		return x.ChartName
	}
	return ""
}

func (x *DeployedAppDetail) GetChartAvatar() string {
	if x != nil {
		return x.ChartAvatar
	}
	return ""
}

func (x *DeployedAppDetail) GetEnvironmentDetail() *EnvironmentDetails {
	if x != nil {
		return x.EnvironmentDetail
	}
	return nil
}

func (x *DeployedAppDetail) GetLastDeployed() *timestamp.Timestamp {
	if x != nil {
		return x.LastDeployed
	}
	return nil
}

func (x *DeployedAppDetail) GetChartVersion() string {
	if x != nil {
		return x.ChartVersion
	}
	return ""
}

func (x *DeployedAppDetail) GetReleaseStatus() string {
	if x != nil {
		return x.ReleaseStatus
	}
	return ""
}

func (x *DeployedAppDetail) GetHome() string {
	if x != nil {
		return x.Home
	}
	return ""
}

type EnvironmentDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterName string `protobuf:"bytes,1,opt,name=clusterName,proto3" json:"clusterName,omitempty"`
	ClusterId   int32  `protobuf:"varint,2,opt,name=clusterId,proto3" json:"clusterId,omitempty"`
	Namespace   string `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
}

func (x *EnvironmentDetails) Reset() {
	*x = EnvironmentDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnvironmentDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnvironmentDetails) ProtoMessage() {}

func (x *EnvironmentDetails) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnvironmentDetails.ProtoReflect.Descriptor instead.
func (*EnvironmentDetails) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{13}
}

func (x *EnvironmentDetails) GetClusterName() string {
	if x != nil {
		return x.ClusterName
	}
	return ""
}

func (x *EnvironmentDetails) GetClusterId() int32 {
	if x != nil {
		return x.ClusterId
	}
	return 0
}

func (x *EnvironmentDetails) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

type AppDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterConfig *ClusterConfig `protobuf:"bytes,1,opt,name=clusterConfig,proto3" json:"clusterConfig,omitempty"`
	Namespace     string         `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	ReleaseName   string         `protobuf:"bytes,3,opt,name=ReleaseName,proto3" json:"ReleaseName,omitempty"`
	// Map key cannot be float, double, bytes, message, or enum types (map<Gvk, Labels> could be a better option)
	ResourceTreeFilter *ResourceTreeFilter `protobuf:"bytes,4,opt,name=resourceTreeFilter,proto3" json:"resourceTreeFilter,omitempty"`
	PreferCache        bool                `protobuf:"varint,5,opt,name=PreferCache,proto3" json:"PreferCache,omitempty"`
	UseFallBack        bool                `protobuf:"varint,6,opt,name=UseFallBack,proto3" json:"UseFallBack,omitempty"`
	CacheConfig        *CacheConfig        `protobuf:"bytes,7,opt,name=cacheConfig,proto3" json:"cacheConfig,omitempty"`
}

func (x *AppDetailRequest) Reset() {
	*x = AppDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppDetailRequest) ProtoMessage() {}

func (x *AppDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppDetailRequest.ProtoReflect.Descriptor instead.
func (*AppDetailRequest) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{14}
}

func (x *AppDetailRequest) GetClusterConfig() *ClusterConfig {
	if x != nil {
		return x.ClusterConfig
	}
	return nil
}

func (x *AppDetailRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *AppDetailRequest) GetReleaseName() string {
	if x != nil {
		return x.ReleaseName
	}
	return ""
}

func (x *AppDetailRequest) GetResourceTreeFilter() *ResourceTreeFilter {
	if x != nil {
		return x.ResourceTreeFilter
	}
	return nil
}

func (x *AppDetailRequest) GetPreferCache() bool {
	if x != nil {
		return x.PreferCache
	}
	return false
}

func (x *AppDetailRequest) GetUseFallBack() bool {
	if x != nil {
		return x.UseFallBack
	}
	return false
}

func (x *AppDetailRequest) GetCacheConfig() *CacheConfig {
	if x != nil {
		return x.CacheConfig
	}
	return nil
}

type AppDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicationStatus    string                `protobuf:"bytes,1,opt,name=applicationStatus,proto3" json:"applicationStatus,omitempty"`
	ReleaseStatus        *ReleaseStatus        `protobuf:"bytes,2,opt,name=releaseStatus,proto3" json:"releaseStatus,omitempty"`
	LastDeployed         *timestamp.Timestamp  `protobuf:"bytes,6,opt,name=lastDeployed,proto3" json:"lastDeployed,omitempty"`
	ChartMetadata        *ChartMetadata        `protobuf:"bytes,7,opt,name=chartMetadata,proto3" json:"chartMetadata,omitempty"`
	ResourceTreeResponse *ResourceTreeResponse `protobuf:"bytes,8,opt,name=resourceTreeResponse,proto3" json:"resourceTreeResponse,omitempty"`
	EnvironmentDetails   *EnvironmentDetails   `protobuf:"bytes,9,opt,name=environmentDetails,proto3" json:"environmentDetails,omitempty"`
	ReleaseExist         bool                  `protobuf:"varint,10,opt,name=ReleaseExist,proto3" json:"ReleaseExist,omitempty"`
}

func (x *AppDetail) Reset() {
	*x = AppDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppDetail) ProtoMessage() {}

func (x *AppDetail) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppDetail.ProtoReflect.Descriptor instead.
func (*AppDetail) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{15}
}

func (x *AppDetail) GetApplicationStatus() string {
	if x != nil {
		return x.ApplicationStatus
	}
	return ""
}

func (x *AppDetail) GetReleaseStatus() *ReleaseStatus {
	if x != nil {
		return x.ReleaseStatus
	}
	return nil
}

func (x *AppDetail) GetLastDeployed() *timestamp.Timestamp {
	if x != nil {
		return x.LastDeployed
	}
	return nil
}

func (x *AppDetail) GetChartMetadata() *ChartMetadata {
	if x != nil {
		return x.ChartMetadata
	}
	return nil
}

func (x *AppDetail) GetResourceTreeResponse() *ResourceTreeResponse {
	if x != nil {
		return x.ResourceTreeResponse
	}
	return nil
}

func (x *AppDetail) GetEnvironmentDetails() *EnvironmentDetails {
	if x != nil {
		return x.EnvironmentDetails
	}
	return nil
}

func (x *AppDetail) GetReleaseExist() bool {
	if x != nil {
		return x.ReleaseExist
	}
	return false
}

type AppStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicationStatus string               `protobuf:"bytes,1,opt,name=ApplicationStatus,proto3" json:"ApplicationStatus,omitempty"`
	ReleaseStatus     string               `protobuf:"bytes,2,opt,name=ReleaseStatus,proto3" json:"ReleaseStatus,omitempty"`
	Description       string               `protobuf:"bytes,3,opt,name=Description,proto3" json:"Description,omitempty"`
	LastDeployed      *timestamp.Timestamp `protobuf:"bytes,4,opt,name=LastDeployed,proto3" json:"LastDeployed,omitempty"`
}

func (x *AppStatus) Reset() {
	*x = AppStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppStatus) ProtoMessage() {}

func (x *AppStatus) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppStatus.ProtoReflect.Descriptor instead.
func (*AppStatus) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{16}
}

func (x *AppStatus) GetApplicationStatus() string {
	if x != nil {
		return x.ApplicationStatus
	}
	return ""
}

func (x *AppStatus) GetReleaseStatus() string {
	if x != nil {
		return x.ReleaseStatus
	}
	return ""
}

func (x *AppStatus) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AppStatus) GetLastDeployed() *timestamp.Timestamp {
	if x != nil {
		return x.LastDeployed
	}
	return nil
}

type ReleaseStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      string `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Message     string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *ReleaseStatus) Reset() {
	*x = ReleaseStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReleaseStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseStatus) ProtoMessage() {}

func (x *ReleaseStatus) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseStatus.ProtoReflect.Descriptor instead.
func (*ReleaseStatus) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{17}
}

func (x *ReleaseStatus) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ReleaseStatus) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ReleaseStatus) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type ChartMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChartName    string   `protobuf:"bytes,1,opt,name=chartName,proto3" json:"chartName,omitempty"`
	ChartVersion string   `protobuf:"bytes,2,opt,name=chartVersion,proto3" json:"chartVersion,omitempty"`
	Home         string   `protobuf:"bytes,3,opt,name=home,proto3" json:"home,omitempty"`
	Sources      []string `protobuf:"bytes,4,rep,name=sources,proto3" json:"sources,omitempty"`
	Description  string   `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// Contains the rendered templates/NOTES.txt
	Notes string `protobuf:"bytes,6,opt,name=notes,proto3" json:"notes,omitempty"`
}

func (x *ChartMetadata) Reset() {
	*x = ChartMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChartMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChartMetadata) ProtoMessage() {}

func (x *ChartMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChartMetadata.ProtoReflect.Descriptor instead.
func (*ChartMetadata) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{18}
}

func (x *ChartMetadata) GetChartName() string {
	if x != nil {
		return x.ChartName
	}
	return ""
}

func (x *ChartMetadata) GetChartVersion() string {
	if x != nil {
		return x.ChartVersion
	}
	return ""
}

func (x *ChartMetadata) GetHome() string {
	if x != nil {
		return x.Home
	}
	return ""
}

func (x *ChartMetadata) GetSources() []string {
	if x != nil {
		return x.Sources
	}
	return nil
}

func (x *ChartMetadata) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ChartMetadata) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

type ResourceTreeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nodes       []*ResourceNode `protobuf:"bytes,1,rep,name=nodes,proto3" json:"nodes,omitempty"`
	PodMetadata []*PodMetadata  `protobuf:"bytes,2,rep,name=podMetadata,proto3" json:"podMetadata,omitempty"`
}

func (x *ResourceTreeResponse) Reset() {
	*x = ResourceTreeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceTreeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceTreeResponse) ProtoMessage() {}

func (x *ResourceTreeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceTreeResponse.ProtoReflect.Descriptor instead.
func (*ResourceTreeResponse) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{19}
}

func (x *ResourceTreeResponse) GetNodes() []*ResourceNode {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *ResourceTreeResponse) GetPodMetadata() []*PodMetadata {
	if x != nil {
		return x.PodMetadata
	}
	return nil
}

type ResourceNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Group           string                  `protobuf:"bytes,1,opt,name=group,proto3" json:"group,omitempty"`
	Version         string                  `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Kind            string                  `protobuf:"bytes,3,opt,name=kind,proto3" json:"kind,omitempty"`
	Namespace       string                  `protobuf:"bytes,4,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name            string                  `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Uid             string                  `protobuf:"bytes,6,opt,name=uid,proto3" json:"uid,omitempty"`
	ParentRefs      []*ResourceRef          `protobuf:"bytes,7,rep,name=parentRefs,proto3" json:"parentRefs,omitempty"`
	NetworkingInfo  *ResourceNetworkingInfo `protobuf:"bytes,8,opt,name=networkingInfo,proto3" json:"networkingInfo,omitempty"`
	ResourceVersion string                  `protobuf:"bytes,9,opt,name=resourceVersion,proto3" json:"resourceVersion,omitempty"`
	Health          *HealthStatus           `protobuf:"bytes,10,opt,name=health,proto3" json:"health,omitempty"`
	IsHibernated    bool                    `protobuf:"varint,11,opt,name=isHibernated,proto3" json:"isHibernated,omitempty"`
	CanBeHibernated bool                    `protobuf:"varint,12,opt,name=canBeHibernated,proto3" json:"canBeHibernated,omitempty"`
	Info            []*InfoItem             `protobuf:"bytes,13,rep,name=info,proto3" json:"info,omitempty"`
	CreatedAt       string                  `protobuf:"bytes,14,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	Port            []int64                 `protobuf:"varint,15,rep,packed,name=port,proto3" json:"port,omitempty"`
	IsHook          bool                    `protobuf:"varint,16,opt,name=isHook,proto3" json:"isHook,omitempty"`
	HookType        string                  `protobuf:"bytes,17,opt,name=hookType,proto3" json:"hookType,omitempty"`
}

func (x *ResourceNode) Reset() {
	*x = ResourceNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceNode) ProtoMessage() {}

func (x *ResourceNode) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceNode.ProtoReflect.Descriptor instead.
func (*ResourceNode) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{20}
}

func (x *ResourceNode) GetGroup() string {
	if x != nil {
		return x.Group
	}
	return ""
}

func (x *ResourceNode) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ResourceNode) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

func (x *ResourceNode) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ResourceNode) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResourceNode) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *ResourceNode) GetParentRefs() []*ResourceRef {
	if x != nil {
		return x.ParentRefs
	}
	return nil
}

func (x *ResourceNode) GetNetworkingInfo() *ResourceNetworkingInfo {
	if x != nil {
		return x.NetworkingInfo
	}
	return nil
}

func (x *ResourceNode) GetResourceVersion() string {
	if x != nil {
		return x.ResourceVersion
	}
	return ""
}

func (x *ResourceNode) GetHealth() *HealthStatus {
	if x != nil {
		return x.Health
	}
	return nil
}

func (x *ResourceNode) GetIsHibernated() bool {
	if x != nil {
		return x.IsHibernated
	}
	return false
}

func (x *ResourceNode) GetCanBeHibernated() bool {
	if x != nil {
		return x.CanBeHibernated
	}
	return false
}

func (x *ResourceNode) GetInfo() []*InfoItem {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *ResourceNode) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *ResourceNode) GetPort() []int64 {
	if x != nil {
		return x.Port
	}
	return nil
}

func (x *ResourceNode) GetIsHook() bool {
	if x != nil {
		return x.IsHook
	}
	return false
}

func (x *ResourceNode) GetHookType() string {
	if x != nil {
		return x.HookType
	}
	return ""
}

type InfoItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *InfoItem) Reset() {
	*x = InfoItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InfoItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InfoItem) ProtoMessage() {}

func (x *InfoItem) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InfoItem.ProtoReflect.Descriptor instead.
func (*InfoItem) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{21}
}

func (x *InfoItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InfoItem) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type HealthStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  string `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *HealthStatus) Reset() {
	*x = HealthStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HealthStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthStatus) ProtoMessage() {}

func (x *HealthStatus) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthStatus.ProtoReflect.Descriptor instead.
func (*HealthStatus) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{22}
}

func (x *HealthStatus) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *HealthStatus) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ResourceNetworkingInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Labels map[string]string `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ResourceNetworkingInfo) Reset() {
	*x = ResourceNetworkingInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceNetworkingInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceNetworkingInfo) ProtoMessage() {}

func (x *ResourceNetworkingInfo) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceNetworkingInfo.ProtoReflect.Descriptor instead.
func (*ResourceNetworkingInfo) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{23}
}

func (x *ResourceNetworkingInfo) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

type ResourceRef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Group     string `protobuf:"bytes,1,opt,name=group,proto3" json:"group,omitempty"`
	Version   string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Kind      string `protobuf:"bytes,3,opt,name=kind,proto3" json:"kind,omitempty"`
	Namespace string `protobuf:"bytes,4,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name      string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Uid       string `protobuf:"bytes,6,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *ResourceRef) Reset() {
	*x = ResourceRef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceRef) ProtoMessage() {}

func (x *ResourceRef) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceRef.ProtoReflect.Descriptor instead.
func (*ResourceRef) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{24}
}

func (x *ResourceRef) GetGroup() string {
	if x != nil {
		return x.Group
	}
	return ""
}

func (x *ResourceRef) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ResourceRef) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

func (x *ResourceRef) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ResourceRef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResourceRef) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type PodMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name                string                    `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Uid                 string                    `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Containers          []string                  `protobuf:"bytes,3,rep,name=containers,proto3" json:"containers,omitempty"`
	InitContainers      []string                  `protobuf:"bytes,4,rep,name=initContainers,proto3" json:"initContainers,omitempty"`
	IsNew               bool                      `protobuf:"varint,5,opt,name=isNew,proto3" json:"isNew,omitempty"`
	EphemeralContainers []*EphemeralContainerData `protobuf:"bytes,6,rep,name=ephemeralContainers,proto3" json:"ephemeralContainers,omitempty"`
}

func (x *PodMetadata) Reset() {
	*x = PodMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PodMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PodMetadata) ProtoMessage() {}

func (x *PodMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PodMetadata.ProtoReflect.Descriptor instead.
func (*PodMetadata) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{25}
}

func (x *PodMetadata) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PodMetadata) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *PodMetadata) GetContainers() []string {
	if x != nil {
		return x.Containers
	}
	return nil
}

func (x *PodMetadata) GetInitContainers() []string {
	if x != nil {
		return x.InitContainers
	}
	return nil
}

func (x *PodMetadata) GetIsNew() bool {
	if x != nil {
		return x.IsNew
	}
	return false
}

func (x *PodMetadata) GetEphemeralContainers() []*EphemeralContainerData {
	if x != nil {
		return x.EphemeralContainers
	}
	return nil
}

type EphemeralContainerData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name       string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	IsExternal bool   `protobuf:"varint,2,opt,name=isExternal,proto3" json:"isExternal,omitempty"`
}

func (x *EphemeralContainerData) Reset() {
	*x = EphemeralContainerData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EphemeralContainerData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EphemeralContainerData) ProtoMessage() {}

func (x *EphemeralContainerData) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EphemeralContainerData.ProtoReflect.Descriptor instead.
func (*EphemeralContainerData) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{26}
}

func (x *EphemeralContainerData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EphemeralContainerData) GetIsExternal() bool {
	if x != nil {
		return x.IsExternal
	}
	return false
}

type HibernateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterConfig    *ClusterConfig      `protobuf:"bytes,1,opt,name=clusterConfig,proto3" json:"clusterConfig,omitempty"`
	ObjectIdentifier []*ObjectIdentifier `protobuf:"bytes,2,rep,name=objectIdentifier,proto3" json:"objectIdentifier,omitempty"`
}

func (x *HibernateRequest) Reset() {
	*x = HibernateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HibernateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HibernateRequest) ProtoMessage() {}

func (x *HibernateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HibernateRequest.ProtoReflect.Descriptor instead.
func (*HibernateRequest) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{27}
}

func (x *HibernateRequest) GetClusterConfig() *ClusterConfig {
	if x != nil {
		return x.ClusterConfig
	}
	return nil
}

func (x *HibernateRequest) GetObjectIdentifier() []*ObjectIdentifier {
	if x != nil {
		return x.ObjectIdentifier
	}
	return nil
}

type ObjectIdentifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Group       string            `protobuf:"bytes,1,opt,name=group,proto3" json:"group,omitempty"`
	Kind        string            `protobuf:"bytes,2,opt,name=kind,proto3" json:"kind,omitempty"`
	Version     string            `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	Name        string            `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Namespace   string            `protobuf:"bytes,5,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Annotations map[string]string `protobuf:"bytes,6,rep,name=annotations,proto3" json:"annotations,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //to move this to a internal object or rename ObjectIdentifier
}

func (x *ObjectIdentifier) Reset() {
	*x = ObjectIdentifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ObjectIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ObjectIdentifier) ProtoMessage() {}

func (x *ObjectIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ObjectIdentifier.ProtoReflect.Descriptor instead.
func (*ObjectIdentifier) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{28}
}

func (x *ObjectIdentifier) GetGroup() string {
	if x != nil {
		return x.Group
	}
	return ""
}

func (x *ObjectIdentifier) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

func (x *ObjectIdentifier) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ObjectIdentifier) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ObjectIdentifier) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ObjectIdentifier) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

type HibernateStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetObject *ObjectIdentifier `protobuf:"bytes,1,opt,name=targetObject,proto3" json:"targetObject,omitempty"`
	Success      bool              `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	ErrorMsg     string            `protobuf:"bytes,3,opt,name=errorMsg,proto3" json:"errorMsg,omitempty"`
}

func (x *HibernateStatus) Reset() {
	*x = HibernateStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HibernateStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HibernateStatus) ProtoMessage() {}

func (x *HibernateStatus) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HibernateStatus.ProtoReflect.Descriptor instead.
func (*HibernateStatus) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{29}
}

func (x *HibernateStatus) GetTargetObject() *ObjectIdentifier {
	if x != nil {
		return x.TargetObject
	}
	return nil
}

func (x *HibernateStatus) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *HibernateStatus) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

type HibernateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status []*HibernateStatus `protobuf:"bytes,1,rep,name=status,proto3" json:"status,omitempty"`
}

func (x *HibernateResponse) Reset() {
	*x = HibernateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HibernateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HibernateResponse) ProtoMessage() {}

func (x *HibernateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HibernateResponse.ProtoReflect.Descriptor instead.
func (*HibernateResponse) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{30}
}

func (x *HibernateResponse) GetStatus() []*HibernateStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

type HelmAppDeploymentDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChartMetadata *ChartMetadata       `protobuf:"bytes,1,opt,name=chartMetadata,proto3" json:"chartMetadata,omitempty"`
	DockerImages  []string             `protobuf:"bytes,2,rep,name=dockerImages,proto3" json:"dockerImages,omitempty"`
	Version       int32                `protobuf:"varint,3,opt,name=version,proto3" json:"version,omitempty"`
	DeployedAt    *timestamp.Timestamp `protobuf:"bytes,4,opt,name=deployedAt,proto3" json:"deployedAt,omitempty"`
	DeployedBy    string               `protobuf:"bytes,5,opt,name=deployedBy,proto3" json:"deployedBy,omitempty"`
	Status        string               `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`
	Message       string               `protobuf:"bytes,7,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *HelmAppDeploymentDetail) Reset() {
	*x = HelmAppDeploymentDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelmAppDeploymentDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelmAppDeploymentDetail) ProtoMessage() {}

func (x *HelmAppDeploymentDetail) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelmAppDeploymentDetail.ProtoReflect.Descriptor instead.
func (*HelmAppDeploymentDetail) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{31}
}

func (x *HelmAppDeploymentDetail) GetChartMetadata() *ChartMetadata {
	if x != nil {
		return x.ChartMetadata
	}
	return nil
}

func (x *HelmAppDeploymentDetail) GetDockerImages() []string {
	if x != nil {
		return x.DockerImages
	}
	return nil
}

func (x *HelmAppDeploymentDetail) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *HelmAppDeploymentDetail) GetDeployedAt() *timestamp.Timestamp {
	if x != nil {
		return x.DeployedAt
	}
	return nil
}

func (x *HelmAppDeploymentDetail) GetDeployedBy() string {
	if x != nil {
		return x.DeployedBy
	}
	return ""
}

func (x *HelmAppDeploymentDetail) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *HelmAppDeploymentDetail) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type HelmAppDeploymentHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeploymentHistory []*HelmAppDeploymentDetail `protobuf:"bytes,1,rep,name=deploymentHistory,proto3" json:"deploymentHistory,omitempty"`
}

func (x *HelmAppDeploymentHistory) Reset() {
	*x = HelmAppDeploymentHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelmAppDeploymentHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelmAppDeploymentHistory) ProtoMessage() {}

func (x *HelmAppDeploymentHistory) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelmAppDeploymentHistory.ProtoReflect.Descriptor instead.
func (*HelmAppDeploymentHistory) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{32}
}

func (x *HelmAppDeploymentHistory) GetDeploymentHistory() []*HelmAppDeploymentDetail {
	if x != nil {
		return x.DeploymentHistory
	}
	return nil
}

type ReleaseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeployedAppDetail *DeployedAppDetail `protobuf:"bytes,1,opt,name=deployedAppDetail,proto3" json:"deployedAppDetail,omitempty"`
	DefaultValues     string             `protobuf:"bytes,2,opt,name=defaultValues,proto3" json:"defaultValues,omitempty"`
	OverrideValues    string             `protobuf:"bytes,3,opt,name=overrideValues,proto3" json:"overrideValues,omitempty"`
	MergedValues      string             `protobuf:"bytes,4,opt,name=mergedValues,proto3" json:"mergedValues,omitempty"`
	Readme            string             `protobuf:"bytes,5,opt,name=readme,proto3" json:"readme,omitempty"`
	ValuesSchemaJson  string             `protobuf:"bytes,6,opt,name=valuesSchemaJson,proto3" json:"valuesSchemaJson,omitempty"`
}

func (x *ReleaseInfo) Reset() {
	*x = ReleaseInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReleaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseInfo) ProtoMessage() {}

func (x *ReleaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseInfo.ProtoReflect.Descriptor instead.
func (*ReleaseInfo) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{33}
}

func (x *ReleaseInfo) GetDeployedAppDetail() *DeployedAppDetail {
	if x != nil {
		return x.DeployedAppDetail
	}
	return nil
}

func (x *ReleaseInfo) GetDefaultValues() string {
	if x != nil {
		return x.DefaultValues
	}
	return ""
}

func (x *ReleaseInfo) GetOverrideValues() string {
	if x != nil {
		return x.OverrideValues
	}
	return ""
}

func (x *ReleaseInfo) GetMergedValues() string {
	if x != nil {
		return x.MergedValues
	}
	return ""
}

func (x *ReleaseInfo) GetReadme() string {
	if x != nil {
		return x.Readme
	}
	return ""
}

func (x *ReleaseInfo) GetValuesSchemaJson() string {
	if x != nil {
		return x.ValuesSchemaJson
	}
	return ""
}

type ObjectRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterConfig    *ClusterConfig    `protobuf:"bytes,1,opt,name=clusterConfig,proto3" json:"clusterConfig,omitempty"`
	ObjectIdentifier *ObjectIdentifier `protobuf:"bytes,2,opt,name=objectIdentifier,proto3" json:"objectIdentifier,omitempty"`
	ReleaseName      string            `protobuf:"bytes,3,opt,name=releaseName,proto3" json:"releaseName,omitempty"`
	ReleaseNamespace string            `protobuf:"bytes,4,opt,name=releaseNamespace,proto3" json:"releaseNamespace,omitempty"`
}

func (x *ObjectRequest) Reset() {
	*x = ObjectRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ObjectRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ObjectRequest) ProtoMessage() {}

func (x *ObjectRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ObjectRequest.ProtoReflect.Descriptor instead.
func (*ObjectRequest) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{34}
}

func (x *ObjectRequest) GetClusterConfig() *ClusterConfig {
	if x != nil {
		return x.ClusterConfig
	}
	return nil
}

func (x *ObjectRequest) GetObjectIdentifier() *ObjectIdentifier {
	if x != nil {
		return x.ObjectIdentifier
	}
	return nil
}

func (x *ObjectRequest) GetReleaseName() string {
	if x != nil {
		return x.ReleaseName
	}
	return ""
}

func (x *ObjectRequest) GetReleaseNamespace() string {
	if x != nil {
		return x.ReleaseNamespace
	}
	return ""
}

type DesiredManifestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Manifest string `protobuf:"bytes,1,opt,name=manifest,proto3" json:"manifest,omitempty"`
}

func (x *DesiredManifestResponse) Reset() {
	*x = DesiredManifestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DesiredManifestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DesiredManifestResponse) ProtoMessage() {}

func (x *DesiredManifestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DesiredManifestResponse.ProtoReflect.Descriptor instead.
func (*DesiredManifestResponse) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{35}
}

func (x *DesiredManifestResponse) GetManifest() string {
	if x != nil {
		return x.Manifest
	}
	return ""
}

type UninstallReleaseResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *UninstallReleaseResponse) Reset() {
	*x = UninstallReleaseResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UninstallReleaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UninstallReleaseResponse) ProtoMessage() {}

func (x *UninstallReleaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UninstallReleaseResponse.ProtoReflect.Descriptor instead.
func (*UninstallReleaseResponse) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{36}
}

func (x *UninstallReleaseResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type ReleaseIdentifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterConfig    *ClusterConfig `protobuf:"bytes,1,opt,name=clusterConfig,proto3" json:"clusterConfig,omitempty"`
	ReleaseName      string         `protobuf:"bytes,2,opt,name=releaseName,proto3" json:"releaseName,omitempty"`
	ReleaseNamespace string         `protobuf:"bytes,3,opt,name=releaseNamespace,proto3" json:"releaseNamespace,omitempty"`
}

func (x *ReleaseIdentifier) Reset() {
	*x = ReleaseIdentifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReleaseIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseIdentifier) ProtoMessage() {}

func (x *ReleaseIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseIdentifier.ProtoReflect.Descriptor instead.
func (*ReleaseIdentifier) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{37}
}

func (x *ReleaseIdentifier) GetClusterConfig() *ClusterConfig {
	if x != nil {
		return x.ClusterConfig
	}
	return nil
}

func (x *ReleaseIdentifier) GetReleaseName() string {
	if x != nil {
		return x.ReleaseName
	}
	return ""
}

func (x *ReleaseIdentifier) GetReleaseNamespace() string {
	if x != nil {
		return x.ReleaseNamespace
	}
	return ""
}

type UpgradeReleaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReleaseIdentifier *ReleaseIdentifier `protobuf:"bytes,1,opt,name=releaseIdentifier,proto3" json:"releaseIdentifier,omitempty"`
	ValuesYaml        string             `protobuf:"bytes,2,opt,name=valuesYaml,proto3" json:"valuesYaml,omitempty"`
	HistoryMax        int32              `protobuf:"varint,3,opt,name=historyMax,proto3" json:"historyMax,omitempty"`
	ChartContent      *ChartContent      `protobuf:"bytes,4,opt,name=chartContent,proto3" json:"chartContent,omitempty"`
	RunInCtx          bool               `protobuf:"varint,5,opt,name=RunInCtx,proto3" json:"RunInCtx,omitempty"`
	K8SVersion        string             `protobuf:"bytes,6,opt,name=K8sVersion,proto3" json:"K8sVersion,omitempty"`
}

func (x *UpgradeReleaseRequest) Reset() {
	*x = UpgradeReleaseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpgradeReleaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeReleaseRequest) ProtoMessage() {}

func (x *UpgradeReleaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeReleaseRequest.ProtoReflect.Descriptor instead.
func (*UpgradeReleaseRequest) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{38}
}

func (x *UpgradeReleaseRequest) GetReleaseIdentifier() *ReleaseIdentifier {
	if x != nil {
		return x.ReleaseIdentifier
	}
	return nil
}

func (x *UpgradeReleaseRequest) GetValuesYaml() string {
	if x != nil {
		return x.ValuesYaml
	}
	return ""
}

func (x *UpgradeReleaseRequest) GetHistoryMax() int32 {
	if x != nil {
		return x.HistoryMax
	}
	return 0
}

func (x *UpgradeReleaseRequest) GetChartContent() *ChartContent {
	if x != nil {
		return x.ChartContent
	}
	return nil
}

func (x *UpgradeReleaseRequest) GetRunInCtx() bool {
	if x != nil {
		return x.RunInCtx
	}
	return false
}

func (x *UpgradeReleaseRequest) GetK8SVersion() string {
	if x != nil {
		return x.K8SVersion
	}
	return ""
}

type UpgradeReleaseResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *UpgradeReleaseResponse) Reset() {
	*x = UpgradeReleaseResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpgradeReleaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeReleaseResponse) ProtoMessage() {}

func (x *UpgradeReleaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeReleaseResponse.ProtoReflect.Descriptor instead.
func (*UpgradeReleaseResponse) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{39}
}

func (x *UpgradeReleaseResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type DeploymentDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReleaseIdentifier *ReleaseIdentifier `protobuf:"bytes,1,opt,name=releaseIdentifier,proto3" json:"releaseIdentifier,omitempty"`
	DeploymentVersion int32              `protobuf:"varint,2,opt,name=deploymentVersion,proto3" json:"deploymentVersion,omitempty"`
}

func (x *DeploymentDetailRequest) Reset() {
	*x = DeploymentDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeploymentDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeploymentDetailRequest) ProtoMessage() {}

func (x *DeploymentDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeploymentDetailRequest.ProtoReflect.Descriptor instead.
func (*DeploymentDetailRequest) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{40}
}

func (x *DeploymentDetailRequest) GetReleaseIdentifier() *ReleaseIdentifier {
	if x != nil {
		return x.ReleaseIdentifier
	}
	return nil
}

func (x *DeploymentDetailRequest) GetDeploymentVersion() int32 {
	if x != nil {
		return x.DeploymentVersion
	}
	return 0
}

type DeploymentDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Manifest   string `protobuf:"bytes,1,opt,name=manifest,proto3" json:"manifest,omitempty"`
	ValuesYaml string `protobuf:"bytes,2,opt,name=valuesYaml,proto3" json:"valuesYaml,omitempty"`
}

func (x *DeploymentDetailResponse) Reset() {
	*x = DeploymentDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeploymentDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeploymentDetailResponse) ProtoMessage() {}

func (x *DeploymentDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeploymentDetailResponse.ProtoReflect.Descriptor instead.
func (*DeploymentDetailResponse) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{41}
}

func (x *DeploymentDetailResponse) GetManifest() string {
	if x != nil {
		return x.Manifest
	}
	return ""
}

func (x *DeploymentDetailResponse) GetValuesYaml() string {
	if x != nil {
		return x.ValuesYaml
	}
	return ""
}

type ChartRepository struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name                    string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Url                     string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Username                string `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Password                string `protobuf:"bytes,4,opt,name=password,proto3" json:"password,omitempty"`
	AllowInsecureConnection bool   `protobuf:"varint,5,opt,name=allowInsecureConnection,proto3" json:"allowInsecureConnection,omitempty"`
}

func (x *ChartRepository) Reset() {
	*x = ChartRepository{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChartRepository) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChartRepository) ProtoMessage() {}

func (x *ChartRepository) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChartRepository.ProtoReflect.Descriptor instead.
func (*ChartRepository) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{42}
}

func (x *ChartRepository) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ChartRepository) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *ChartRepository) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ChartRepository) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *ChartRepository) GetAllowInsecureConnection() bool {
	if x != nil {
		return x.AllowInsecureConnection
	}
	return false
}

type InstallReleaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReleaseIdentifier          *ReleaseIdentifier  `protobuf:"bytes,1,opt,name=releaseIdentifier,proto3" json:"releaseIdentifier,omitempty"`
	ChartName                  string              `protobuf:"bytes,2,opt,name=chartName,proto3" json:"chartName,omitempty"`
	ChartVersion               string              `protobuf:"bytes,3,opt,name=chartVersion,proto3" json:"chartVersion,omitempty"`
	ValuesYaml                 string              `protobuf:"bytes,4,opt,name=valuesYaml,proto3" json:"valuesYaml,omitempty"`
	ChartRepository            *ChartRepository    `protobuf:"bytes,5,opt,name=chartRepository,proto3" json:"chartRepository,omitempty"`
	K8SVersion                 string              `protobuf:"bytes,6,opt,name=K8sVersion,proto3" json:"K8sVersion,omitempty"`
	HistoryMax                 int32               `protobuf:"varint,7,opt,name=historyMax,proto3" json:"historyMax,omitempty"`
	RegistryCredential         *RegistryCredential `protobuf:"bytes,8,opt,name=RegistryCredential,proto3" json:"RegistryCredential,omitempty"`
	IsOCIRepo                  bool                `protobuf:"varint,9,opt,name=IsOCIRepo,proto3" json:"IsOCIRepo,omitempty"`
	InstallAppVersionHistoryId int32               `protobuf:"varint,10,opt,name=installAppVersionHistoryId,proto3" json:"installAppVersionHistoryId,omitempty"`
	ChartContent               *ChartContent       `protobuf:"bytes,11,opt,name=chartContent,proto3" json:"chartContent,omitempty"`
	AppName                    string              `protobuf:"bytes,12,opt,name=appName,proto3" json:"appName,omitempty"`
}

func (x *InstallReleaseRequest) Reset() {
	*x = InstallReleaseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InstallReleaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstallReleaseRequest) ProtoMessage() {}

func (x *InstallReleaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstallReleaseRequest.ProtoReflect.Descriptor instead.
func (*InstallReleaseRequest) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{43}
}

func (x *InstallReleaseRequest) GetReleaseIdentifier() *ReleaseIdentifier {
	if x != nil {
		return x.ReleaseIdentifier
	}
	return nil
}

func (x *InstallReleaseRequest) GetChartName() string {
	if x != nil {
		return x.ChartName
	}
	return ""
}

func (x *InstallReleaseRequest) GetChartVersion() string {
	if x != nil {
		return x.ChartVersion
	}
	return ""
}

func (x *InstallReleaseRequest) GetValuesYaml() string {
	if x != nil {
		return x.ValuesYaml
	}
	return ""
}

func (x *InstallReleaseRequest) GetChartRepository() *ChartRepository {
	if x != nil {
		return x.ChartRepository
	}
	return nil
}

func (x *InstallReleaseRequest) GetK8SVersion() string {
	if x != nil {
		return x.K8SVersion
	}
	return ""
}

func (x *InstallReleaseRequest) GetHistoryMax() int32 {
	if x != nil {
		return x.HistoryMax
	}
	return 0
}

func (x *InstallReleaseRequest) GetRegistryCredential() *RegistryCredential {
	if x != nil {
		return x.RegistryCredential
	}
	return nil
}

func (x *InstallReleaseRequest) GetIsOCIRepo() bool {
	if x != nil {
		return x.IsOCIRepo
	}
	return false
}

func (x *InstallReleaseRequest) GetInstallAppVersionHistoryId() int32 {
	if x != nil {
		return x.InstallAppVersionHistoryId
	}
	return 0
}

func (x *InstallReleaseRequest) GetChartContent() *ChartContent {
	if x != nil {
		return x.ChartContent
	}
	return nil
}

func (x *InstallReleaseRequest) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

type BulkInstallReleaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BulkInstallReleaseRequest []*InstallReleaseRequest `protobuf:"bytes,1,rep,name=BulkInstallReleaseRequest,proto3" json:"BulkInstallReleaseRequest,omitempty"`
}

func (x *BulkInstallReleaseRequest) Reset() {
	*x = BulkInstallReleaseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulkInstallReleaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkInstallReleaseRequest) ProtoMessage() {}

func (x *BulkInstallReleaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkInstallReleaseRequest.ProtoReflect.Descriptor instead.
func (*BulkInstallReleaseRequest) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{44}
}

func (x *BulkInstallReleaseRequest) GetBulkInstallReleaseRequest() []*InstallReleaseRequest {
	if x != nil {
		return x.BulkInstallReleaseRequest
	}
	return nil
}

type InstallReleaseResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *InstallReleaseResponse) Reset() {
	*x = InstallReleaseResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InstallReleaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstallReleaseResponse) ProtoMessage() {}

func (x *InstallReleaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstallReleaseResponse.ProtoReflect.Descriptor instead.
func (*InstallReleaseResponse) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{45}
}

func (x *InstallReleaseResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type BooleanResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *BooleanResponse) Reset() {
	*x = BooleanResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BooleanResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BooleanResponse) ProtoMessage() {}

func (x *BooleanResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BooleanResponse.ProtoReflect.Descriptor instead.
func (*BooleanResponse) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{46}
}

func (x *BooleanResponse) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

type RollbackReleaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReleaseIdentifier *ReleaseIdentifier `protobuf:"bytes,1,opt,name=releaseIdentifier,proto3" json:"releaseIdentifier,omitempty"`
	Version           int32              `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *RollbackReleaseRequest) Reset() {
	*x = RollbackReleaseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RollbackReleaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RollbackReleaseRequest) ProtoMessage() {}

func (x *RollbackReleaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RollbackReleaseRequest.ProtoReflect.Descriptor instead.
func (*RollbackReleaseRequest) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{47}
}

func (x *RollbackReleaseRequest) GetReleaseIdentifier() *ReleaseIdentifier {
	if x != nil {
		return x.ReleaseIdentifier
	}
	return nil
}

func (x *RollbackReleaseRequest) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

type TemplateChartResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GeneratedManifest string `protobuf:"bytes,1,opt,name=generatedManifest,proto3" json:"generatedManifest,omitempty"`
	AppName           string `protobuf:"bytes,2,opt,name=appName,proto3" json:"appName,omitempty"`
}

func (x *TemplateChartResponse) Reset() {
	*x = TemplateChartResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemplateChartResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplateChartResponse) ProtoMessage() {}

func (x *TemplateChartResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplateChartResponse.ProtoReflect.Descriptor instead.
func (*TemplateChartResponse) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{48}
}

func (x *TemplateChartResponse) GetGeneratedManifest() string {
	if x != nil {
		return x.GeneratedManifest
	}
	return ""
}

func (x *TemplateChartResponse) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

type BulkTemplateChartResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BulkTemplateChartResponse []*TemplateChartResponse `protobuf:"bytes,1,rep,name=BulkTemplateChartResponse,proto3" json:"BulkTemplateChartResponse,omitempty"`
}

func (x *BulkTemplateChartResponse) Reset() {
	*x = BulkTemplateChartResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulkTemplateChartResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkTemplateChartResponse) ProtoMessage() {}

func (x *BulkTemplateChartResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkTemplateChartResponse.ProtoReflect.Descriptor instead.
func (*BulkTemplateChartResponse) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{49}
}

func (x *BulkTemplateChartResponse) GetBulkTemplateChartResponse() []*TemplateChartResponse {
	if x != nil {
		return x.BulkTemplateChartResponse
	}
	return nil
}

type TemplateChartResponseWithChart struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemplateChartResponse *TemplateChartResponse `protobuf:"bytes,1,opt,name=templateChartResponse,proto3" json:"templateChartResponse,omitempty"`
	ChartBytes            *ChartContent          `protobuf:"bytes,2,opt,name=chartBytes,proto3" json:"chartBytes,omitempty"`
}

func (x *TemplateChartResponseWithChart) Reset() {
	*x = TemplateChartResponseWithChart{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemplateChartResponseWithChart) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplateChartResponseWithChart) ProtoMessage() {}

func (x *TemplateChartResponseWithChart) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplateChartResponseWithChart.ProtoReflect.Descriptor instead.
func (*TemplateChartResponseWithChart) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{50}
}

func (x *TemplateChartResponseWithChart) GetTemplateChartResponse() *TemplateChartResponse {
	if x != nil {
		return x.TemplateChartResponse
	}
	return nil
}

func (x *TemplateChartResponseWithChart) GetChartBytes() *ChartContent {
	if x != nil {
		return x.ChartBytes
	}
	return nil
}

type HelmInstallCustomRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ValuesYaml        string             `protobuf:"bytes,1,opt,name=valuesYaml,proto3" json:"valuesYaml,omitempty"`
	ChartContent      *ChartContent      `protobuf:"bytes,2,opt,name=chartContent,proto3" json:"chartContent,omitempty"`
	ReleaseIdentifier *ReleaseIdentifier `protobuf:"bytes,3,opt,name=releaseIdentifier,proto3" json:"releaseIdentifier,omitempty"`
	RunInCtx          bool               `protobuf:"varint,4,opt,name=RunInCtx,proto3" json:"RunInCtx,omitempty"`
	K8SVersion        string             `protobuf:"bytes,5,opt,name=K8sVersion,proto3" json:"K8sVersion,omitempty"`
}

func (x *HelmInstallCustomRequest) Reset() {
	*x = HelmInstallCustomRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelmInstallCustomRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelmInstallCustomRequest) ProtoMessage() {}

func (x *HelmInstallCustomRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelmInstallCustomRequest.ProtoReflect.Descriptor instead.
func (*HelmInstallCustomRequest) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{51}
}

func (x *HelmInstallCustomRequest) GetValuesYaml() string {
	if x != nil {
		return x.ValuesYaml
	}
	return ""
}

func (x *HelmInstallCustomRequest) GetChartContent() *ChartContent {
	if x != nil {
		return x.ChartContent
	}
	return nil
}

func (x *HelmInstallCustomRequest) GetReleaseIdentifier() *ReleaseIdentifier {
	if x != nil {
		return x.ReleaseIdentifier
	}
	return nil
}

func (x *HelmInstallCustomRequest) GetRunInCtx() bool {
	if x != nil {
		return x.RunInCtx
	}
	return false
}

func (x *HelmInstallCustomRequest) GetK8SVersion() string {
	if x != nil {
		return x.K8SVersion
	}
	return ""
}

type HelmInstallCustomResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *HelmInstallCustomResponse) Reset() {
	*x = HelmInstallCustomResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelmInstallCustomResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelmInstallCustomResponse) ProtoMessage() {}

func (x *HelmInstallCustomResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelmInstallCustomResponse.ProtoReflect.Descriptor instead.
func (*HelmInstallCustomResponse) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{52}
}

func (x *HelmInstallCustomResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type ChartContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Content []byte `protobuf:"bytes,1,opt,name=Content,proto3" json:"Content,omitempty"`
}

func (x *ChartContent) Reset() {
	*x = ChartContent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChartContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChartContent) ProtoMessage() {}

func (x *ChartContent) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChartContent.ProtoReflect.Descriptor instead.
func (*ChartContent) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{53}
}

func (x *ChartContent) GetContent() []byte {
	if x != nil {
		return x.Content
	}
	return nil
}

type Gvk struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Group   string `protobuf:"bytes,1,opt,name=Group,proto3" json:"Group,omitempty"`
	Version string `protobuf:"bytes,2,opt,name=Version,proto3" json:"Version,omitempty"`
	Kind    string `protobuf:"bytes,3,opt,name=Kind,proto3" json:"Kind,omitempty"`
}

func (x *Gvk) Reset() {
	*x = Gvk{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Gvk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Gvk) ProtoMessage() {}

func (x *Gvk) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Gvk.ProtoReflect.Descriptor instead.
func (*Gvk) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{54}
}

func (x *Gvk) GetGroup() string {
	if x != nil {
		return x.Group
	}
	return ""
}

func (x *Gvk) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Gvk) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

type ResourceFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Gvk                *Gvk                `protobuf:"bytes,1,opt,name=gvk,proto3" json:"gvk,omitempty"`
	ResourceIdentifier *ResourceIdentifier `protobuf:"bytes,2,opt,name=resourceIdentifier,proto3" json:"resourceIdentifier,omitempty"`
}

func (x *ResourceFilter) Reset() {
	*x = ResourceFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceFilter) ProtoMessage() {}

func (x *ResourceFilter) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceFilter.ProtoReflect.Descriptor instead.
func (*ResourceFilter) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{55}
}

func (x *ResourceFilter) GetGvk() *Gvk {
	if x != nil {
		return x.Gvk
	}
	return nil
}

func (x *ResourceFilter) GetResourceIdentifier() *ResourceIdentifier {
	if x != nil {
		return x.ResourceIdentifier
	}
	return nil
}

type ResourceIdentifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Labels map[string]string `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ResourceIdentifier) Reset() {
	*x = ResourceIdentifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceIdentifier) ProtoMessage() {}

func (x *ResourceIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceIdentifier.ProtoReflect.Descriptor instead.
func (*ResourceIdentifier) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{56}
}

func (x *ResourceIdentifier) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

type ResourceTreeFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GlobalFilter    *ResourceIdentifier `protobuf:"bytes,1,opt,name=globalFilter,proto3" json:"globalFilter,omitempty"`
	ResourceFilters []*ResourceFilter   `protobuf:"bytes,2,rep,name=resourceFilters,proto3" json:"resourceFilters,omitempty"`
}

func (x *ResourceTreeFilter) Reset() {
	*x = ResourceTreeFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceTreeFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceTreeFilter) ProtoMessage() {}

func (x *ResourceTreeFilter) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceTreeFilter.ProtoReflect.Descriptor instead.
func (*ResourceTreeFilter) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{57}
}

func (x *ResourceTreeFilter) GetGlobalFilter() *ResourceIdentifier {
	if x != nil {
		return x.GlobalFilter
	}
	return nil
}

func (x *ResourceTreeFilter) GetResourceFilters() []*ResourceFilter {
	if x != nil {
		return x.ResourceFilters
	}
	return nil
}

type ChartNotesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Notes string `protobuf:"bytes,1,opt,name=notes,proto3" json:"notes,omitempty"`
}

func (x *ChartNotesResponse) Reset() {
	*x = ChartNotesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChartNotesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChartNotesResponse) ProtoMessage() {}

func (x *ChartNotesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChartNotesResponse.ProtoReflect.Descriptor instead.
func (*ChartNotesResponse) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{58}
}

func (x *ChartNotesResponse) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

type OCIRegistryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Chart              []byte              `protobuf:"bytes,1,opt,name=Chart,proto3" json:"Chart,omitempty"`
	ChartName          string              `protobuf:"bytes,2,opt,name=ChartName,proto3" json:"ChartName,omitempty"`
	ChartVersion       string              `protobuf:"bytes,3,opt,name=ChartVersion,proto3" json:"ChartVersion,omitempty"`
	IsInsecure         bool                `protobuf:"varint,4,opt,name=IsInsecure,proto3" json:"IsInsecure,omitempty"`
	RegistryCredential *RegistryCredential `protobuf:"bytes,5,opt,name=RegistryCredential,proto3" json:"RegistryCredential,omitempty"`
}

func (x *OCIRegistryRequest) Reset() {
	*x = OCIRegistryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OCIRegistryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OCIRegistryRequest) ProtoMessage() {}

func (x *OCIRegistryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OCIRegistryRequest.ProtoReflect.Descriptor instead.
func (*OCIRegistryRequest) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{59}
}

func (x *OCIRegistryRequest) GetChart() []byte {
	if x != nil {
		return x.Chart
	}
	return nil
}

func (x *OCIRegistryRequest) GetChartName() string {
	if x != nil {
		return x.ChartName
	}
	return ""
}

func (x *OCIRegistryRequest) GetChartVersion() string {
	if x != nil {
		return x.ChartVersion
	}
	return ""
}

func (x *OCIRegistryRequest) GetIsInsecure() bool {
	if x != nil {
		return x.IsInsecure
	}
	return false
}

func (x *OCIRegistryRequest) GetRegistryCredential() *RegistryCredential {
	if x != nil {
		return x.RegistryCredential
	}
	return nil
}

type RegistryCredential struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RegistryUrl            string                  `protobuf:"bytes,1,opt,name=RegistryUrl,proto3" json:"RegistryUrl,omitempty"`
	Username               string                  `protobuf:"bytes,2,opt,name=Username,proto3" json:"Username,omitempty"`
	Password               string                  `protobuf:"bytes,3,opt,name=Password,proto3" json:"Password,omitempty"`
	AwsRegion              string                  `protobuf:"bytes,4,opt,name=AwsRegion,proto3" json:"AwsRegion,omitempty"`
	AccessKey              string                  `protobuf:"bytes,5,opt,name=AccessKey,proto3" json:"AccessKey,omitempty"`
	SecretKey              string                  `protobuf:"bytes,6,opt,name=SecretKey,proto3" json:"SecretKey,omitempty"`
	RegistryType           string                  `protobuf:"bytes,7,opt,name=RegistryType,proto3" json:"RegistryType,omitempty"`
	RepoName               string                  `protobuf:"bytes,8,opt,name=RepoName,proto3" json:"RepoName,omitempty"`
	IsPublic               bool                    `protobuf:"varint,9,opt,name=IsPublic,proto3" json:"IsPublic,omitempty"`
	RemoteConnectionConfig *RemoteConnectionConfig `protobuf:"bytes,10,opt,name=RemoteConnectionConfig,proto3" json:"RemoteConnectionConfig,omitempty"`
	Connection             string                  `protobuf:"bytes,11,opt,name=Connection,proto3" json:"Connection,omitempty"`
	RegistryName           string                  `protobuf:"bytes,12,opt,name=RegistryName,proto3" json:"RegistryName,omitempty"`
	RegistryCertificate    string                  `protobuf:"bytes,13,opt,name=RegistryCertificate,proto3" json:"RegistryCertificate,omitempty"`
	CredentialsType        string                  `protobuf:"bytes,14,opt,name=CredentialsType,proto3" json:"CredentialsType,omitempty"`
}

func (x *RegistryCredential) Reset() {
	*x = RegistryCredential{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegistryCredential) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegistryCredential) ProtoMessage() {}

func (x *RegistryCredential) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegistryCredential.ProtoReflect.Descriptor instead.
func (*RegistryCredential) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{60}
}

func (x *RegistryCredential) GetRegistryUrl() string {
	if x != nil {
		return x.RegistryUrl
	}
	return ""
}

func (x *RegistryCredential) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *RegistryCredential) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *RegistryCredential) GetAwsRegion() string {
	if x != nil {
		return x.AwsRegion
	}
	return ""
}

func (x *RegistryCredential) GetAccessKey() string {
	if x != nil {
		return x.AccessKey
	}
	return ""
}

func (x *RegistryCredential) GetSecretKey() string {
	if x != nil {
		return x.SecretKey
	}
	return ""
}

func (x *RegistryCredential) GetRegistryType() string {
	if x != nil {
		return x.RegistryType
	}
	return ""
}

func (x *RegistryCredential) GetRepoName() string {
	if x != nil {
		return x.RepoName
	}
	return ""
}

func (x *RegistryCredential) GetIsPublic() bool {
	if x != nil {
		return x.IsPublic
	}
	return false
}

func (x *RegistryCredential) GetRemoteConnectionConfig() *RemoteConnectionConfig {
	if x != nil {
		return x.RemoteConnectionConfig
	}
	return nil
}

func (x *RegistryCredential) GetConnection() string {
	if x != nil {
		return x.Connection
	}
	return ""
}

func (x *RegistryCredential) GetRegistryName() string {
	if x != nil {
		return x.RegistryName
	}
	return ""
}

func (x *RegistryCredential) GetRegistryCertificate() string {
	if x != nil {
		return x.RegistryCertificate
	}
	return ""
}

func (x *RegistryCredential) GetCredentialsType() string {
	if x != nil {
		return x.CredentialsType
	}
	return ""
}

type ProxyConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProxyUrl string `protobuf:"bytes,1,opt,name=ProxyUrl,proto3" json:"ProxyUrl,omitempty"`
}

func (x *ProxyConfig) Reset() {
	*x = ProxyConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProxyConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProxyConfig) ProtoMessage() {}

func (x *ProxyConfig) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProxyConfig.ProtoReflect.Descriptor instead.
func (*ProxyConfig) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{61}
}

func (x *ProxyConfig) GetProxyUrl() string {
	if x != nil {
		return x.ProxyUrl
	}
	return ""
}

type SSHTunnelConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SSHServerAddress string `protobuf:"bytes,1,opt,name=SSHServerAddress,proto3" json:"SSHServerAddress,omitempty"`
	SSHUsername      string `protobuf:"bytes,2,opt,name=SSHUsername,proto3" json:"SSHUsername,omitempty"`
	SSHPassword      string `protobuf:"bytes,3,opt,name=SSHPassword,proto3" json:"SSHPassword,omitempty"`
	SSHAuthKey       string `protobuf:"bytes,4,opt,name=SSHAuthKey,proto3" json:"SSHAuthKey,omitempty"`
}

func (x *SSHTunnelConfig) Reset() {
	*x = SSHTunnelConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SSHTunnelConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SSHTunnelConfig) ProtoMessage() {}

func (x *SSHTunnelConfig) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SSHTunnelConfig.ProtoReflect.Descriptor instead.
func (*SSHTunnelConfig) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{62}
}

func (x *SSHTunnelConfig) GetSSHServerAddress() string {
	if x != nil {
		return x.SSHServerAddress
	}
	return ""
}

func (x *SSHTunnelConfig) GetSSHUsername() string {
	if x != nil {
		return x.SSHUsername
	}
	return ""
}

func (x *SSHTunnelConfig) GetSSHPassword() string {
	if x != nil {
		return x.SSHPassword
	}
	return ""
}

func (x *SSHTunnelConfig) GetSSHAuthKey() string {
	if x != nil {
		return x.SSHAuthKey
	}
	return ""
}

type RemoteConnectionConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RemoteConnectionMethod RemoteConnectionMethod `protobuf:"varint,1,opt,name=RemoteConnectionMethod,proto3,enum=RemoteConnectionMethod" json:"RemoteConnectionMethod,omitempty"`
	ProxyConfig            *ProxyConfig           `protobuf:"bytes,2,opt,name=ProxyConfig,proto3" json:"ProxyConfig,omitempty"`
	SSHTunnelConfig        *SSHTunnelConfig       `protobuf:"bytes,3,opt,name=SSHTunnelConfig,proto3" json:"SSHTunnelConfig,omitempty"`
}

func (x *RemoteConnectionConfig) Reset() {
	*x = RemoteConnectionConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoteConnectionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoteConnectionConfig) ProtoMessage() {}

func (x *RemoteConnectionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoteConnectionConfig.ProtoReflect.Descriptor instead.
func (*RemoteConnectionConfig) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{63}
}

func (x *RemoteConnectionConfig) GetRemoteConnectionMethod() RemoteConnectionMethod {
	if x != nil {
		return x.RemoteConnectionMethod
	}
	return RemoteConnectionMethod_PROXY
}

func (x *RemoteConnectionConfig) GetProxyConfig() *ProxyConfig {
	if x != nil {
		return x.ProxyConfig
	}
	return nil
}

func (x *RemoteConnectionConfig) GetSSHTunnelConfig() *SSHTunnelConfig {
	if x != nil {
		return x.SSHTunnelConfig
	}
	return nil
}

type OCIRegistryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsLoggedIn bool                     `protobuf:"varint,1,opt,name=IsLoggedIn,proto3" json:"IsLoggedIn,omitempty"`
	PushResult *OCIRegistryPushResponse `protobuf:"bytes,2,opt,name=PushResult,proto3" json:"PushResult,omitempty"`
}

func (x *OCIRegistryResponse) Reset() {
	*x = OCIRegistryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OCIRegistryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OCIRegistryResponse) ProtoMessage() {}

func (x *OCIRegistryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OCIRegistryResponse.ProtoReflect.Descriptor instead.
func (*OCIRegistryResponse) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{64}
}

func (x *OCIRegistryResponse) GetIsLoggedIn() bool {
	if x != nil {
		return x.IsLoggedIn
	}
	return false
}

func (x *OCIRegistryResponse) GetPushResult() *OCIRegistryPushResponse {
	if x != nil {
		return x.PushResult
	}
	return nil
}

type OCIRegistryPushResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Digest    string `protobuf:"bytes,1,opt,name=Digest,proto3" json:"Digest,omitempty"`
	PushedURL string `protobuf:"bytes,2,opt,name=PushedURL,proto3" json:"PushedURL,omitempty"`
}

func (x *OCIRegistryPushResponse) Reset() {
	*x = OCIRegistryPushResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_applist_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OCIRegistryPushResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OCIRegistryPushResponse) ProtoMessage() {}

func (x *OCIRegistryPushResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_applist_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OCIRegistryPushResponse.ProtoReflect.Descriptor instead.
func (*OCIRegistryPushResponse) Descriptor() ([]byte, []int) {
	return file_grpc_applist_proto_rawDescGZIP(), []int{65}
}

func (x *OCIRegistryPushResponse) GetDigest() string {
	if x != nil {
		return x.Digest
	}
	return ""
}

func (x *OCIRegistryPushResponse) GetPushedURL() string {
	if x != nil {
		return x.PushedURL
	}
	return ""
}

var File_grpc_applist_proto protoreflect.FileDescriptor

var file_grpc_applist_proto_rawDesc = []byte{
	0x0a, 0x12, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xde, 0x02, 0x0a, 0x0d, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x70, 0x69, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61,
	0x70, 0x69, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x20, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x34, 0x0a, 0x15, 0x69, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x53, 0x6b, 0x69,
	0x70, 0x54, 0x4c, 0x53, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x15, 0x69, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x53, 0x6b, 0x69, 0x70, 0x54, 0x4c,
	0x53, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x44, 0x61,
	0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x65, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x65, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a,
	0x06, 0x63, 0x61, 0x44, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63,
	0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4f, 0x0a, 0x16, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x16,
	0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x3c, 0x0a, 0x0e, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x08, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x43, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x08, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x73, 0x22, 0x88, 0x03, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x54, 0x72, 0x65, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x34, 0x0a, 0x0d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x12, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x72, 0x65, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x72, 0x65, 0x65,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x12, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x54, 0x72, 0x65, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x50, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x43, 0x61, 0x63, 0x68, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0b, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x43, 0x61, 0x63, 0x68, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x55, 0x73, 0x65, 0x46, 0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0b, 0x55, 0x73, 0x65, 0x46, 0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b, 0x12, 0x2e,
	0x0a, 0x0b, 0x63, 0x61, 0x63, 0x68, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x43, 0x61, 0x63, 0x68, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x0b, 0x63, 0x61, 0x63, 0x68, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3f,
	0x0a, 0x11, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x4f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x11, 0x6f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x22,
	0x7b, 0x0a, 0x0b, 0x43, 0x61, 0x63, 0x68, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x20,
	0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x70, 0x61, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x70, 0x61, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x22, 0x98, 0x02, 0x0a,
	0x1b, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x72, 0x65, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x0d,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x0d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x4f, 0x0a, 0x16, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x16, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x2e, 0x0a, 0x0b, 0x63, 0x61, 0x63, 0x68, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x43, 0x61, 0x63, 0x68, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0b, 0x63, 0x61, 0x63, 0x68, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x43, 0x61, 0x63,
	0x68, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x43, 0x61, 0x63, 0x68, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x46, 0x61, 0x6c, 0x6c,
	0x42, 0x61, 0x63, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x55, 0x73, 0x65, 0x46,
	0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b, 0x22, 0x8e, 0x01, 0x0a, 0x16, 0x45, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x22, 0xa7, 0x01, 0x0a, 0x0f, 0x44, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x65, 0x64, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x40, 0x0a, 0x11,
	0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x64, 0x41, 0x70, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x65, 0x64, 0x41, 0x70, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x11, 0x44, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x65, 0x64, 0x41, 0x70, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1c,
	0x0a, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x65, 0x64, 0x22, 0xa5, 0x01, 0x0a, 0x13, 0x46, 0x6c, 0x75, 0x78, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0f, 0x46, 0x6c, 0x75, 0x78,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x46, 0x6c, 0x75, 0x78, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x46, 0x6c, 0x75, 0x78, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x73, 0x67,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x73, 0x67,
	0x12, 0x18, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x65, 0x64, 0x22, 0x96, 0x02, 0x0a, 0x0f, 0x46,
	0x6c, 0x75, 0x78, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x79, 0x6e, 0x63, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x79, 0x6e, 0x63,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x41, 0x0a, 0x11, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f,
	0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x11, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d,
	0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x34, 0x0a, 0x15, 0x66, 0x6c, 0x75,
	0x78, 0x41, 0x70, 0x70, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x66, 0x6c, 0x75, 0x78, 0x41, 0x70,
	0x70, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x32, 0x0a, 0x14, 0x68, 0x65, 0x6c, 0x6d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x68,
	0x65, 0x6c, 0x6d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x22, 0x9a, 0x02, 0x0a, 0x14, 0x46, 0x6c, 0x75, 0x78, 0x41, 0x70, 0x70, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x0d,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x0d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x49, 0x73, 0x4b, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x69, 0x7a, 0x65, 0x41, 0x70, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x49, 0x73,
	0x4b, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x41, 0x70, 0x70, 0x12, 0x2e, 0x0a, 0x0b,
	0x63, 0x61, 0x63, 0x68, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0c, 0x2e, 0x43, 0x61, 0x63, 0x68, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x0b, 0x63, 0x61, 0x63, 0x68, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x20, 0x0a, 0x0b,
	0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x43, 0x61, 0x63, 0x68, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0b, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x43, 0x61, 0x63, 0x68, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x55, 0x73, 0x65, 0x46, 0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0b, 0x55, 0x73, 0x65, 0x46, 0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b,
	0x22, 0xc4, 0x02, 0x0a, 0x0d, 0x46, 0x6c, 0x75, 0x78, 0x41, 0x70, 0x70, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x12, 0x3a, 0x0a, 0x0f, 0x66, 0x6c, 0x75, 0x78, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x46, 0x6c,
	0x75, 0x78, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x66,
	0x6c, 0x75, 0x78, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x46,
	0x0a, 0x13, 0x46, 0x6c, 0x75, 0x78, 0x41, 0x70, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x46, 0x6c,
	0x75, 0x78, 0x41, 0x70, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x13, 0x46, 0x6c, 0x75, 0x78, 0x41, 0x70, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x49, 0x0a, 0x14, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x54, 0x72, 0x65, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54,
	0x72, 0x65, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x14, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x72, 0x65, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x2c, 0x0a, 0x11, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x36, 0x0a, 0x16, 0x6c, 0x61, 0x73, 0x74, 0x4f, 0x62, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x47,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x16, 0x6c, 0x61, 0x73, 0x74, 0x4f, 0x62, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x47, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x5f, 0x0a, 0x13, 0x46, 0x6c, 0x75, 0x78, 0x41,
	0x70, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x16,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x18,
	0x0a, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xe4, 0x02, 0x0a, 0x11, 0x44, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x65, 0x64, 0x41, 0x70, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x14,
	0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x63, 0x68, 0x61, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x68, 0x61, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x63, 0x68, 0x61, 0x72, 0x74, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x72, 0x74, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x41,
	0x0a, 0x11, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x45, 0x6e, 0x76, 0x69,
	0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x11,
	0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0x3e, 0x0a, 0x0c, 0x4c, 0x61, 0x73, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x65,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0c, 0x4c, 0x61, 0x73, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x65,
	0x64, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x72, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x68, 0x61, 0x72, 0x74, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x68,
	0x6f, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x6d, 0x65, 0x22,
	0x72, 0x0a, 0x12, 0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x22, 0xc1, 0x02, 0x0a, 0x10, 0x41, 0x70, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x0d, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x0d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1c,
	0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x43,
	0x0a, 0x12, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x72, 0x65, 0x65, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x72, 0x65, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52,
	0x12, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x72, 0x65, 0x65, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x43, 0x61, 0x63,
	0x68, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x43, 0x61, 0x63, 0x68, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x46, 0x61, 0x6c, 0x6c,
	0x42, 0x61, 0x63, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x55, 0x73, 0x65, 0x46,
	0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b, 0x12, 0x2e, 0x0a, 0x0b, 0x63, 0x61, 0x63, 0x68, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x43,
	0x61, 0x63, 0x68, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0b, 0x63, 0x61, 0x63, 0x68,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x99, 0x03, 0x0a, 0x09, 0x41, 0x70, 0x70, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x2c, 0x0a, 0x11, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x34, 0x0a, 0x0d, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x52, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x72, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3e, 0x0a, 0x0c, 0x6c, 0x61, 0x73,
	0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x6c, 0x61, 0x73,
	0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x64, 0x12, 0x34, 0x0a, 0x0d, 0x63, 0x68, 0x61,
	0x72, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0e, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x52, 0x0d, 0x63, 0x68, 0x61, 0x72, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x49, 0x0a, 0x14, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x72, 0x65, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x72, 0x65, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x52, 0x14, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x72,
	0x65, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x43, 0x0a, 0x12, 0x65, 0x6e,
	0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e,
	0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x12, 0x65, 0x6e, 0x76,
	0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x22, 0x0a, 0x0c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x45, 0x78, 0x69, 0x73, 0x74, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x45, 0x78,
	0x69, 0x73, 0x74, 0x22, 0xc1, 0x01, 0x0a, 0x09, 0x41, 0x70, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x2c, 0x0a, 0x11, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x24, 0x0a, 0x0d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3e, 0x0a, 0x0c, 0x4c, 0x61, 0x73, 0x74, 0x44,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x4c, 0x61, 0x73, 0x74, 0x44,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x64, 0x22, 0x63, 0x0a, 0x0d, 0x52, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xb7, 0x01, 0x0a,
	0x0d, 0x43, 0x68, 0x61, 0x72, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1c,
	0x0a, 0x09, 0x63, 0x68, 0x61, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x68, 0x61, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c,
	0x63, 0x68, 0x61, 0x72, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x63, 0x68, 0x61, 0x72, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x68, 0x6f, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x22, 0x6b, 0x0a, 0x14, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x54, 0x72, 0x65, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x05, 0x6e, 0x6f,
	0x64, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x0b, 0x70, 0x6f, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x50, 0x6f, 0x64, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0b, 0x70, 0x6f, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x22, 0xa9, 0x04, 0x0a, 0x0c, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4e, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x0a,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x0c, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x66, 0x52, 0x0a,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x73, 0x12, 0x3f, 0x0a, 0x0e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x28, 0x0a, 0x0f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x12, 0x22, 0x0a, 0x0c,
	0x69, 0x73, 0x48, 0x69, 0x62, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x48, 0x69, 0x62, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x64,
	0x12, 0x28, 0x0a, 0x0f, 0x63, 0x61, 0x6e, 0x42, 0x65, 0x48, 0x69, 0x62, 0x65, 0x72, 0x6e, 0x61,
	0x74, 0x65, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x63, 0x61, 0x6e, 0x42, 0x65,
	0x48, 0x69, 0x62, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x12, 0x1d, 0x0a, 0x04, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18,
	0x0f, 0x20, 0x03, 0x28, 0x03, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x69,
	0x73, 0x48, 0x6f, 0x6f, 0x6b, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x48,
	0x6f, 0x6f, 0x6b, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x6f, 0x6f, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x6f, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x22,
	0x34, 0x0a, 0x08, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x40, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x90, 0x01, 0x0a, 0x16, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x3b, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a,
	0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x95, 0x01, 0x0a, 0x0b, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x66, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6b, 0x69,
	0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x1c,
	0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75,
	0x69, 0x64, 0x22, 0xdc, 0x01, 0x0a, 0x0b, 0x50, 0x6f, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x69, 0x6e, 0x69, 0x74,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0e, 0x69, 0x6e, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x69, 0x73, 0x4e, 0x65, 0x77, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x05, 0x69, 0x73, 0x4e, 0x65, 0x77, 0x12, 0x49, 0x0a, 0x13, 0x65, 0x70, 0x68, 0x65, 0x6d, 0x65,
	0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x45, 0x70, 0x68, 0x65, 0x6d, 0x65, 0x72, 0x61, 0x6c, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x13, 0x65, 0x70,
	0x68, 0x65, 0x6d, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x73, 0x22, 0x4c, 0x0a, 0x16, 0x45, 0x70, 0x68, 0x65, 0x6d, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x69, 0x73, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x22,
	0x87, 0x01, 0x0a, 0x10, 0x48, 0x69, 0x62, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x0d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x43, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3d, 0x0a, 0x10, 0x6f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x10, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x8e, 0x02, 0x0a, 0x10, 0x4f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x14,
	0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x12, 0x44, 0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x4f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x2e, 0x41, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x3e, 0x0a, 0x10, 0x41, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x7e, 0x0a, 0x0f, 0x48, 0x69,
	0x62, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x35, 0x0a,
	0x0c, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x0c, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1a,
	0x0a, 0x08, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x73, 0x67, 0x22, 0x3d, 0x0a, 0x11, 0x48, 0x69,
	0x62, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x28, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x48, 0x69, 0x62, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x9b, 0x02, 0x0a, 0x17, 0x48, 0x65,
	0x6c, 0x6d, 0x41, 0x70, 0x70, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x34, 0x0a, 0x0d, 0x63, 0x68, 0x61, 0x72, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x43,
	0x68, 0x61, 0x72, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0d, 0x63, 0x68,
	0x61, 0x72, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x22, 0x0a, 0x0c, 0x64,
	0x6f, 0x63, 0x6b, 0x65, 0x72, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0c, 0x64, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12,
	0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x0a, 0x64, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x65, 0x64, 0x41, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x65,
	0x64, 0x42, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x65, 0x64, 0x42, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x62, 0x0a, 0x18, 0x48, 0x65, 0x6c, 0x6d, 0x41,
	0x70, 0x70, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x12, 0x46, 0x0a, 0x11, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x48, 0x65, 0x6c, 0x6d, 0x41, 0x70, 0x70, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x11, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x22, 0x85, 0x02, 0x0a, 0x0b,
	0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x40, 0x0a, 0x11, 0x64,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x64, 0x41, 0x70, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x65,
	0x64, 0x41, 0x70, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x11, 0x64, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x65, 0x64, 0x41, 0x70, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x24, 0x0a,
	0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x6d,
	0x65, 0x72, 0x67, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x64, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x65, 0x61, 0x64, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x73, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x4a, 0x73, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x4a,
	0x73, 0x6f, 0x6e, 0x22, 0xd2, 0x01, 0x0a, 0x0d, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x0d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x43,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3d, 0x0a, 0x10, 0x6f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x10, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x10,
	0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x22, 0x35, 0x0a, 0x17, 0x44, 0x65, 0x73, 0x69,
	0x72, 0x65, 0x64, 0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x22,
	0x34, 0x0a, 0x18, 0x55, 0x6e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x97, 0x01, 0x0a, 0x11, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x0d, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x0d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x22,
	0x88, 0x02, 0x0a, 0x15, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x40, 0x0a, 0x11, 0x72, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x11, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x59, 0x61, 0x6d, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x59, 0x61, 0x6d, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x68,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4d, 0x61, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4d, 0x61, 0x78, 0x12, 0x31, 0x0a, 0x0c, 0x63,
	0x68, 0x61, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0d, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x52, 0x0c, 0x63, 0x68, 0x61, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x52, 0x75, 0x6e, 0x49, 0x6e, 0x43, 0x74, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x52, 0x75, 0x6e, 0x49, 0x6e, 0x43, 0x74, 0x78, 0x12, 0x1e, 0x0a, 0x0a, 0x4b, 0x38,
	0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x4b, 0x38, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x32, 0x0a, 0x16, 0x55, 0x70,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x89,
	0x01, 0x0a, 0x17, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x40, 0x0a, 0x11, 0x72, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x11, 0x72, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x11,
	0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x56, 0x0a, 0x18, 0x44, 0x65,
	0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x6e, 0x69, 0x66, 0x65,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x61, 0x6e, 0x69, 0x66, 0x65,
	0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x59, 0x61, 0x6d, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x59, 0x61,
	0x6d, 0x6c, 0x22, 0xa9, 0x01, 0x0a, 0x0f, 0x43, 0x68, 0x61, 0x72, 0x74, 0x52, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x12, 0x38, 0x0a, 0x17, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xa7,
	0x04, 0x0a, 0x15, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x40, 0x0a, 0x11, 0x72, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x11, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x68,
	0x61, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x68, 0x61, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x72,
	0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x63, 0x68, 0x61, 0x72, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x59, 0x61, 0x6d, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x59, 0x61, 0x6d, 0x6c, 0x12, 0x3a, 0x0a, 0x0f,
	0x63, 0x68, 0x61, 0x72, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x74, 0x52, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x0f, 0x63, 0x68, 0x61, 0x72, 0x74, 0x52, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x4b, 0x38, 0x73, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x4b, 0x38,
	0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x68, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x4d, 0x61, 0x78, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x68, 0x69,
	0x73, 0x74, 0x6f, 0x72, 0x79, 0x4d, 0x61, 0x78, 0x12, 0x43, 0x0a, 0x12, 0x52, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x72, 0x79, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x43,
	0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x12, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x72, 0x79, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x12, 0x1c, 0x0a,
	0x09, 0x49, 0x73, 0x4f, 0x43, 0x49, 0x52, 0x65, 0x70, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x09, 0x49, 0x73, 0x4f, 0x43, 0x49, 0x52, 0x65, 0x70, 0x6f, 0x12, 0x3e, 0x0a, 0x1a, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x1a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x0c, 0x63,
	0x68, 0x61, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0d, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x52, 0x0c, 0x63, 0x68, 0x61, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x71, 0x0a, 0x19, 0x42, 0x75, 0x6c, 0x6b,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x54, 0x0a, 0x19, 0x42, 0x75, 0x6c, 0x6b, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6c, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x19, 0x42, 0x75, 0x6c, 0x6b, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x32, 0x0a, 0x16, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22,
	0x29, 0x0a, 0x0f, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x74, 0x0a, 0x16, 0x52, 0x6f,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x40, 0x0a, 0x11, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x52, 0x11, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x22, 0x5f, 0x0a, 0x15, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x72,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x67, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x4d,
	0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0x71, 0x0a, 0x19, 0x42, 0x75, 0x6c, 0x6b, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x43, 0x68, 0x61, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54,
	0x0a, 0x19, 0x42, 0x75, 0x6c, 0x6b, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x68,
	0x61, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x72,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x19, 0x42, 0x75, 0x6c, 0x6b, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x9d, 0x01, 0x0a, 0x1e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x43, 0x68, 0x61, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x57, 0x69,
	0x74, 0x68, 0x43, 0x68, 0x61, 0x72, 0x74, 0x12, 0x4c, 0x0a, 0x15, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x43, 0x68, 0x61, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x15,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x72, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x72, 0x74, 0x42, 0x79,
	0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x43, 0x68, 0x61, 0x72,
	0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x72, 0x74, 0x42,
	0x79, 0x74, 0x65, 0x73, 0x22, 0xeb, 0x01, 0x0a, 0x18, 0x48, 0x65, 0x6c, 0x6d, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6c, 0x6c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x59, 0x61, 0x6d, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x59, 0x61, 0x6d,
	0x6c, 0x12, 0x31, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x74, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x0c, 0x63, 0x68, 0x61, 0x72, 0x74, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x40, 0x0a, 0x11, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x52, 0x11, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x52, 0x75, 0x6e, 0x49, 0x6e, 0x43,
	0x74, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x52, 0x75, 0x6e, 0x49, 0x6e, 0x43,
	0x74, 0x78, 0x12, 0x1e, 0x0a, 0x0a, 0x4b, 0x38, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x4b, 0x38, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x22, 0x35, 0x0a, 0x19, 0x48, 0x65, 0x6c, 0x6d, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x28, 0x0a, 0x0c, 0x43, 0x68, 0x61,
	0x72, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x22, 0x49, 0x0a, 0x03, 0x47, 0x76, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x12, 0x18, 0x0a, 0x07, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x4b, 0x69,
	0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4b, 0x69, 0x6e, 0x64, 0x22, 0x6d,
	0x0a, 0x0e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x16, 0x0a, 0x03, 0x67, 0x76, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x04, 0x2e,
	0x47, 0x76, 0x6b, 0x52, 0x03, 0x67, 0x76, 0x6b, 0x12, 0x43, 0x0a, 0x12, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x12, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x88, 0x01,
	0x0a, 0x12, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x39, 0x0a,
	0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x88, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x72, 0x65, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x37, 0x0a, 0x0c, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x0c, 0x67, 0x6c, 0x6f, 0x62,
	0x61, 0x6c, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0f, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x52, 0x0f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x22, 0x2a, 0x0a, 0x12, 0x43, 0x68, 0x61, 0x72, 0x74, 0x4e, 0x6f, 0x74, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x74,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x22,
	0xd1, 0x01, 0x0a, 0x12, 0x4f, 0x43, 0x49, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x43, 0x68, 0x61, 0x72, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x43, 0x68, 0x61, 0x72, 0x74, 0x12, 0x1c, 0x0a, 0x09,
	0x43, 0x68, 0x61, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x43, 0x68, 0x61, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x43, 0x68,
	0x61, 0x72, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x43, 0x68, 0x61, 0x72, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e,
	0x0a, 0x0a, 0x49, 0x73, 0x49, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0a, 0x49, 0x73, 0x49, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x12, 0x43,
	0x0a, 0x12, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x52, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x72, 0x79, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x52,
	0x12, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x61, 0x6c, 0x22, 0x95, 0x04, 0x0a, 0x12, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79,
	0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08,
	0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x41, 0x77, 0x73, 0x52, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x41, 0x77, 0x73, 0x52, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4b, 0x65, 0x79,
	0x12, 0x1c, 0x0a, 0x09, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x22,
	0x0a, 0x0c, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x52, 0x65, 0x70, 0x6f, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x52, 0x65, 0x70, 0x6f, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x49, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x49, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x12, 0x4f, 0x0a, 0x16, 0x52, 0x65,
	0x6d, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x52, 0x65, 0x6d,
	0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x16, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x30, 0x0a, 0x13, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x43, 0x65, 0x72, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x65, 0x12, 0x28, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x43, 0x72, 0x65, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x54, 0x79, 0x70, 0x65, 0x22, 0x29, 0x0a, 0x0b, 0x50,
	0x72, 0x6f, 0x78, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x72,
	0x6f, 0x78, 0x79, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x72,
	0x6f, 0x78, 0x79, 0x55, 0x72, 0x6c, 0x22, 0xa1, 0x01, 0x0a, 0x0f, 0x53, 0x53, 0x48, 0x54, 0x75,
	0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2a, 0x0a, 0x10, 0x53, 0x53,
	0x48, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x53, 0x53, 0x48, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x53, 0x53, 0x48, 0x55, 0x73, 0x65,
	0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x53, 0x53, 0x48,
	0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x53, 0x53, 0x48, 0x50,
	0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x53,
	0x53, 0x48, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x53,
	0x48, 0x41, 0x75, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x53, 0x53, 0x48, 0x41, 0x75, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x22, 0xd5, 0x01, 0x0a, 0x16, 0x52,
	0x65, 0x6d, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4f, 0x0a, 0x16, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x16,
	0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x2e, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x50, 0x72,
	0x6f, 0x78, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0b, 0x50, 0x72, 0x6f, 0x78, 0x79,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3a, 0x0a, 0x0f, 0x53, 0x53, 0x48, 0x54, 0x75, 0x6e,
	0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x53, 0x53, 0x48, 0x54, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x0f, 0x53, 0x53, 0x48, 0x54, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x22, 0x6f, 0x0a, 0x13, 0x4f, 0x43, 0x49, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x49, 0x73, 0x4c,
	0x6f, 0x67, 0x67, 0x65, 0x64, 0x49, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x49,
	0x73, 0x4c, 0x6f, 0x67, 0x67, 0x65, 0x64, 0x49, 0x6e, 0x12, 0x38, 0x0a, 0x0a, 0x50, 0x75, 0x73,
	0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x4f, 0x43, 0x49, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x50, 0x75, 0x73, 0x68, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x4f, 0x0a, 0x17, 0x4f, 0x43, 0x49, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x72, 0x79, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x44, 0x69, 0x67, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x44, 0x69, 0x67, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x50, 0x75, 0x73, 0x68, 0x65, 0x64,
	0x55, 0x52, 0x4c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x50, 0x75, 0x73, 0x68, 0x65,
	0x64, 0x55, 0x52, 0x4c, 0x2a, 0x38, 0x0a, 0x16, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x09,
	0x0a, 0x05, 0x50, 0x52, 0x4f, 0x58, 0x59, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x53, 0x53, 0x48,
	0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x49, 0x52, 0x45, 0x43, 0x54, 0x10, 0x02, 0x32, 0xd9,
	0x0f, 0x0a, 0x12, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x39, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x0f, 0x2e, 0x41, 0x70, 0x70, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x10, 0x2e, 0x44, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x65, 0x64, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x00, 0x30, 0x01,
	0x12, 0x41, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x6c, 0x75, 0x78, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x0f, 0x2e, 0x41, 0x70, 0x70, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x46, 0x6c, 0x75, 0x78,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0x00, 0x30, 0x01, 0x12, 0x2f, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x11, 0x2e, 0x41, 0x70, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0a, 0x2e, 0x41, 0x70, 0x70, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x22, 0x00, 0x12, 0x2f, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x11, 0x2e, 0x41, 0x70, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0a, 0x2e, 0x41, 0x70, 0x70, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x00, 0x12, 0x31, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x56, 0x32, 0x12, 0x11, 0x2e, 0x41, 0x70, 0x70, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0a, 0x2e, 0x41, 0x70, 0x70,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x00, 0x12, 0x34, 0x0a, 0x09, 0x48, 0x69, 0x62, 0x65,
	0x72, 0x6e, 0x61, 0x74, 0x65, 0x12, 0x11, 0x2e, 0x48, 0x69, 0x62, 0x65, 0x72, 0x6e, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x48, 0x69, 0x62, 0x65, 0x72,
	0x6e, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x36,
	0x0a, 0x0b, 0x55, 0x6e, 0x48, 0x69, 0x62, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x12, 0x11, 0x2e,
	0x48, 0x69, 0x62, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x12, 0x2e, 0x48, 0x69, 0x62, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x46, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x11,
	0x2e, 0x41, 0x70, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x19, 0x2e, 0x48, 0x65, 0x6c, 0x6d, 0x41, 0x70, 0x70, 0x44, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x22, 0x00, 0x12, 0x32,
	0x0a, 0x0d, 0x47, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x59, 0x61, 0x6d, 0x6c, 0x12,
	0x11, 0x2e, 0x41, 0x70, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x0c, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x22, 0x00, 0x12, 0x40, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x44, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64,
	0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x44, 0x65, 0x73, 0x69, 0x72,
	0x65, 0x64, 0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x43, 0x0a, 0x10, 0x55, 0x6e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x12, 0x12, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x1a, 0x19, 0x2e, 0x55,
	0x6e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x43, 0x0a, 0x0e, 0x55, 0x70, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x12, 0x16, 0x2e, 0x55, 0x70,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4c,
	0x0a, 0x13, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x18, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x19, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x43, 0x0a, 0x0e,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x12, 0x16,
	0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c,
	0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x50, 0x0a, 0x1b, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x57, 0x69, 0x74, 0x68, 0x43, 0x68, 0x61, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x16, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x55, 0x70, 0x67, 0x72, 0x61,
	0x64, 0x65, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x3c, 0x0a, 0x12, 0x49, 0x73, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x12, 0x12, 0x2e, 0x52, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x1a, 0x10, 0x2e,
	0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x3e, 0x0a, 0x0f, 0x52, 0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x12, 0x17, 0x2e, 0x52, 0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x10, 0x2e,
	0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x41, 0x0a, 0x0d, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61,
	0x72, 0x74, 0x12, 0x16, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x4d, 0x0a, 0x11, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x43, 0x68, 0x61, 0x72, 0x74, 0x42, 0x75, 0x6c, 0x6b, 0x12, 0x1a, 0x2e, 0x42, 0x75, 0x6c, 0x6b,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x1d, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x43,
	0x68, 0x61, 0x72, 0x74, 0x41, 0x6e, 0x64, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x43,
	0x68, 0x61, 0x72, 0x74, 0x12, 0x16, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x52, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x57, 0x69, 0x74, 0x68, 0x43, 0x68, 0x61, 0x72, 0x74, 0x22, 0x00, 0x12,
	0x58, 0x0a, 0x1d, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x57, 0x69, 0x74, 0x68, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x68, 0x61, 0x72, 0x74,
	0x12, 0x19, 0x2e, 0x48, 0x65, 0x6c, 0x6d, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x48, 0x65,
	0x6c, 0x6d, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x39, 0x0a, 0x08, 0x47, 0x65, 0x74,
	0x4e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x16, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e,
	0x43, 0x68, 0x61, 0x72, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x52, 0x0a, 0x1d, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x57, 0x69, 0x74, 0x68, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x43, 0x68, 0x61, 0x72, 0x74, 0x12, 0x16, 0x2e, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e,
	0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x42, 0x0a, 0x13, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x4f, 0x43, 0x49, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x12,
	0x13, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x61, 0x6c, 0x1a, 0x14, 0x2e, 0x4f, 0x43, 0x49, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x49, 0x0a, 0x1a,
	0x50, 0x75, 0x73, 0x68, 0x48, 0x65, 0x6c, 0x6d, 0x43, 0x68, 0x61, 0x72, 0x74, 0x54, 0x6f, 0x4f,
	0x43, 0x49, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x12, 0x13, 0x2e, 0x4f, 0x43, 0x49,
	0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x14, 0x2e, 0x4f, 0x43, 0x49, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5c, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x72, 0x65, 0x65, 0x46, 0x6f, 0x72, 0x45, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x1c,
	0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x72, 0x65, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x72, 0x65, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3b, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x46, 0x6c, 0x75, 0x78,
	0x41, 0x70, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x15, 0x2e, 0x46, 0x6c, 0x75, 0x78,
	0x41, 0x70, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x0e, 0x2e, 0x46, 0x6c, 0x75, 0x78, 0x41, 0x70, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x22, 0x00, 0x12, 0x3d, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x12, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x1a, 0x12, 0x2e, 0x44, 0x65,
	0x70, 0x6c, 0x6f, 0x79, 0x65, 0x64, 0x41, 0x70, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22,
	0x00, 0x12, 0x57, 0x0a, 0x23, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x54, 0x72, 0x65, 0x65, 0x55, 0x73, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x17, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x72, 0x65, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x15, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x72, 0x65, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x33, 0x5a, 0x31, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x64, 0x65, 0x76, 0x74, 0x72, 0x6f, 0x6e,
	0x2d, 0x6c, 0x61, 0x62, 0x73, 0x2f, 0x6b, 0x75, 0x62, 0x65, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x62,
	0x65, 0x61, 0x6e, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_grpc_applist_proto_rawDescOnce sync.Once
	file_grpc_applist_proto_rawDescData = file_grpc_applist_proto_rawDesc
)

func file_grpc_applist_proto_rawDescGZIP() []byte {
	file_grpc_applist_proto_rawDescOnce.Do(func() {
		file_grpc_applist_proto_rawDescData = protoimpl.X.CompressGZIP(file_grpc_applist_proto_rawDescData)
	})
	return file_grpc_applist_proto_rawDescData
}

var file_grpc_applist_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_grpc_applist_proto_msgTypes = make([]protoimpl.MessageInfo, 69)
var file_grpc_applist_proto_goTypes = []interface{}{
	(RemoteConnectionMethod)(0),            // 0: RemoteConnectionMethod
	(*ClusterConfig)(nil),                  // 1: ClusterConfig
	(*AppListRequest)(nil),                 // 2: AppListRequest
	(*GetResourceTreeRequest)(nil),         // 3: GetResourceTreeRequest
	(*CacheConfig)(nil),                    // 4: CacheConfig
	(*ExternalResourceTreeRequest)(nil),    // 5: ExternalResourceTreeRequest
	(*ExternalResourceDetail)(nil),         // 6: ExternalResourceDetail
	(*DeployedAppList)(nil),                // 7: DeployedAppList
	(*FluxApplicationList)(nil),            // 8: FluxApplicationList
	(*FluxApplication)(nil),                // 9: FluxApplication
	(*FluxAppDetailRequest)(nil),           // 10: FluxAppDetailRequest
	(*FluxAppDetail)(nil),                  // 11: FluxAppDetail
	(*FluxAppStatusDetail)(nil),            // 12: FluxAppStatusDetail
	(*DeployedAppDetail)(nil),              // 13: DeployedAppDetail
	(*EnvironmentDetails)(nil),             // 14: EnvironmentDetails
	(*AppDetailRequest)(nil),               // 15: AppDetailRequest
	(*AppDetail)(nil),                      // 16: AppDetail
	(*AppStatus)(nil),                      // 17: AppStatus
	(*ReleaseStatus)(nil),                  // 18: ReleaseStatus
	(*ChartMetadata)(nil),                  // 19: ChartMetadata
	(*ResourceTreeResponse)(nil),           // 20: ResourceTreeResponse
	(*ResourceNode)(nil),                   // 21: ResourceNode
	(*InfoItem)(nil),                       // 22: InfoItem
	(*HealthStatus)(nil),                   // 23: HealthStatus
	(*ResourceNetworkingInfo)(nil),         // 24: ResourceNetworkingInfo
	(*ResourceRef)(nil),                    // 25: ResourceRef
	(*PodMetadata)(nil),                    // 26: PodMetadata
	(*EphemeralContainerData)(nil),         // 27: EphemeralContainerData
	(*HibernateRequest)(nil),               // 28: HibernateRequest
	(*ObjectIdentifier)(nil),               // 29: ObjectIdentifier
	(*HibernateStatus)(nil),                // 30: HibernateStatus
	(*HibernateResponse)(nil),              // 31: HibernateResponse
	(*HelmAppDeploymentDetail)(nil),        // 32: HelmAppDeploymentDetail
	(*HelmAppDeploymentHistory)(nil),       // 33: HelmAppDeploymentHistory
	(*ReleaseInfo)(nil),                    // 34: ReleaseInfo
	(*ObjectRequest)(nil),                  // 35: ObjectRequest
	(*DesiredManifestResponse)(nil),        // 36: DesiredManifestResponse
	(*UninstallReleaseResponse)(nil),       // 37: UninstallReleaseResponse
	(*ReleaseIdentifier)(nil),              // 38: ReleaseIdentifier
	(*UpgradeReleaseRequest)(nil),          // 39: UpgradeReleaseRequest
	(*UpgradeReleaseResponse)(nil),         // 40: UpgradeReleaseResponse
	(*DeploymentDetailRequest)(nil),        // 41: DeploymentDetailRequest
	(*DeploymentDetailResponse)(nil),       // 42: DeploymentDetailResponse
	(*ChartRepository)(nil),                // 43: ChartRepository
	(*InstallReleaseRequest)(nil),          // 44: InstallReleaseRequest
	(*BulkInstallReleaseRequest)(nil),      // 45: BulkInstallReleaseRequest
	(*InstallReleaseResponse)(nil),         // 46: InstallReleaseResponse
	(*BooleanResponse)(nil),                // 47: BooleanResponse
	(*RollbackReleaseRequest)(nil),         // 48: RollbackReleaseRequest
	(*TemplateChartResponse)(nil),          // 49: TemplateChartResponse
	(*BulkTemplateChartResponse)(nil),      // 50: BulkTemplateChartResponse
	(*TemplateChartResponseWithChart)(nil), // 51: TemplateChartResponseWithChart
	(*HelmInstallCustomRequest)(nil),       // 52: HelmInstallCustomRequest
	(*HelmInstallCustomResponse)(nil),      // 53: HelmInstallCustomResponse
	(*ChartContent)(nil),                   // 54: ChartContent
	(*Gvk)(nil),                            // 55: Gvk
	(*ResourceFilter)(nil),                 // 56: ResourceFilter
	(*ResourceIdentifier)(nil),             // 57: ResourceIdentifier
	(*ResourceTreeFilter)(nil),             // 58: ResourceTreeFilter
	(*ChartNotesResponse)(nil),             // 59: ChartNotesResponse
	(*OCIRegistryRequest)(nil),             // 60: OCIRegistryRequest
	(*RegistryCredential)(nil),             // 61: RegistryCredential
	(*ProxyConfig)(nil),                    // 62: ProxyConfig
	(*SSHTunnelConfig)(nil),                // 63: SSHTunnelConfig
	(*RemoteConnectionConfig)(nil),         // 64: RemoteConnectionConfig
	(*OCIRegistryResponse)(nil),            // 65: OCIRegistryResponse
	(*OCIRegistryPushResponse)(nil),        // 66: OCIRegistryPushResponse
	nil,                                    // 67: ResourceNetworkingInfo.LabelsEntry
	nil,                                    // 68: ObjectIdentifier.AnnotationsEntry
	nil,                                    // 69: ResourceIdentifier.LabelsEntry
	(*timestamp.Timestamp)(nil),            // 70: google.protobuf.Timestamp
}
var file_grpc_applist_proto_depIdxs = []int32{
	64,  // 0: ClusterConfig.RemoteConnectionConfig:type_name -> RemoteConnectionConfig
	1,   // 1: AppListRequest.clusters:type_name -> ClusterConfig
	1,   // 2: GetResourceTreeRequest.clusterConfig:type_name -> ClusterConfig
	58,  // 3: GetResourceTreeRequest.resourceTreeFilter:type_name -> ResourceTreeFilter
	4,   // 4: GetResourceTreeRequest.cacheConfig:type_name -> CacheConfig
	29,  // 5: GetResourceTreeRequest.objectIdentifiers:type_name -> ObjectIdentifier
	1,   // 6: ExternalResourceTreeRequest.clusterConfig:type_name -> ClusterConfig
	6,   // 7: ExternalResourceTreeRequest.externalResourceDetail:type_name -> ExternalResourceDetail
	4,   // 8: ExternalResourceTreeRequest.cacheConfig:type_name -> CacheConfig
	13,  // 9: DeployedAppList.DeployedAppDetail:type_name -> DeployedAppDetail
	9,   // 10: FluxApplicationList.FluxApplication:type_name -> FluxApplication
	14,  // 11: FluxApplication.environmentDetail:type_name -> EnvironmentDetails
	1,   // 12: FluxAppDetailRequest.clusterConfig:type_name -> ClusterConfig
	4,   // 13: FluxAppDetailRequest.cacheConfig:type_name -> CacheConfig
	9,   // 14: FluxAppDetail.fluxApplication:type_name -> FluxApplication
	12,  // 15: FluxAppDetail.FluxAppStatusDetail:type_name -> FluxAppStatusDetail
	20,  // 16: FluxAppDetail.resourceTreeResponse:type_name -> ResourceTreeResponse
	14,  // 17: DeployedAppDetail.environmentDetail:type_name -> EnvironmentDetails
	70,  // 18: DeployedAppDetail.LastDeployed:type_name -> google.protobuf.Timestamp
	1,   // 19: AppDetailRequest.clusterConfig:type_name -> ClusterConfig
	58,  // 20: AppDetailRequest.resourceTreeFilter:type_name -> ResourceTreeFilter
	4,   // 21: AppDetailRequest.cacheConfig:type_name -> CacheConfig
	18,  // 22: AppDetail.releaseStatus:type_name -> ReleaseStatus
	70,  // 23: AppDetail.lastDeployed:type_name -> google.protobuf.Timestamp
	19,  // 24: AppDetail.chartMetadata:type_name -> ChartMetadata
	20,  // 25: AppDetail.resourceTreeResponse:type_name -> ResourceTreeResponse
	14,  // 26: AppDetail.environmentDetails:type_name -> EnvironmentDetails
	70,  // 27: AppStatus.LastDeployed:type_name -> google.protobuf.Timestamp
	21,  // 28: ResourceTreeResponse.nodes:type_name -> ResourceNode
	26,  // 29: ResourceTreeResponse.podMetadata:type_name -> PodMetadata
	25,  // 30: ResourceNode.parentRefs:type_name -> ResourceRef
	24,  // 31: ResourceNode.networkingInfo:type_name -> ResourceNetworkingInfo
	23,  // 32: ResourceNode.health:type_name -> HealthStatus
	22,  // 33: ResourceNode.info:type_name -> InfoItem
	67,  // 34: ResourceNetworkingInfo.labels:type_name -> ResourceNetworkingInfo.LabelsEntry
	27,  // 35: PodMetadata.ephemeralContainers:type_name -> EphemeralContainerData
	1,   // 36: HibernateRequest.clusterConfig:type_name -> ClusterConfig
	29,  // 37: HibernateRequest.objectIdentifier:type_name -> ObjectIdentifier
	68,  // 38: ObjectIdentifier.annotations:type_name -> ObjectIdentifier.AnnotationsEntry
	29,  // 39: HibernateStatus.targetObject:type_name -> ObjectIdentifier
	30,  // 40: HibernateResponse.status:type_name -> HibernateStatus
	19,  // 41: HelmAppDeploymentDetail.chartMetadata:type_name -> ChartMetadata
	70,  // 42: HelmAppDeploymentDetail.deployedAt:type_name -> google.protobuf.Timestamp
	32,  // 43: HelmAppDeploymentHistory.deploymentHistory:type_name -> HelmAppDeploymentDetail
	13,  // 44: ReleaseInfo.deployedAppDetail:type_name -> DeployedAppDetail
	1,   // 45: ObjectRequest.clusterConfig:type_name -> ClusterConfig
	29,  // 46: ObjectRequest.objectIdentifier:type_name -> ObjectIdentifier
	1,   // 47: ReleaseIdentifier.clusterConfig:type_name -> ClusterConfig
	38,  // 48: UpgradeReleaseRequest.releaseIdentifier:type_name -> ReleaseIdentifier
	54,  // 49: UpgradeReleaseRequest.chartContent:type_name -> ChartContent
	38,  // 50: DeploymentDetailRequest.releaseIdentifier:type_name -> ReleaseIdentifier
	38,  // 51: InstallReleaseRequest.releaseIdentifier:type_name -> ReleaseIdentifier
	43,  // 52: InstallReleaseRequest.chartRepository:type_name -> ChartRepository
	61,  // 53: InstallReleaseRequest.RegistryCredential:type_name -> RegistryCredential
	54,  // 54: InstallReleaseRequest.chartContent:type_name -> ChartContent
	44,  // 55: BulkInstallReleaseRequest.BulkInstallReleaseRequest:type_name -> InstallReleaseRequest
	38,  // 56: RollbackReleaseRequest.releaseIdentifier:type_name -> ReleaseIdentifier
	49,  // 57: BulkTemplateChartResponse.BulkTemplateChartResponse:type_name -> TemplateChartResponse
	49,  // 58: TemplateChartResponseWithChart.templateChartResponse:type_name -> TemplateChartResponse
	54,  // 59: TemplateChartResponseWithChart.chartBytes:type_name -> ChartContent
	54,  // 60: HelmInstallCustomRequest.chartContent:type_name -> ChartContent
	38,  // 61: HelmInstallCustomRequest.releaseIdentifier:type_name -> ReleaseIdentifier
	55,  // 62: ResourceFilter.gvk:type_name -> Gvk
	57,  // 63: ResourceFilter.resourceIdentifier:type_name -> ResourceIdentifier
	69,  // 64: ResourceIdentifier.labels:type_name -> ResourceIdentifier.LabelsEntry
	57,  // 65: ResourceTreeFilter.globalFilter:type_name -> ResourceIdentifier
	56,  // 66: ResourceTreeFilter.resourceFilters:type_name -> ResourceFilter
	61,  // 67: OCIRegistryRequest.RegistryCredential:type_name -> RegistryCredential
	64,  // 68: RegistryCredential.RemoteConnectionConfig:type_name -> RemoteConnectionConfig
	0,   // 69: RemoteConnectionConfig.RemoteConnectionMethod:type_name -> RemoteConnectionMethod
	62,  // 70: RemoteConnectionConfig.ProxyConfig:type_name -> ProxyConfig
	63,  // 71: RemoteConnectionConfig.SSHTunnelConfig:type_name -> SSHTunnelConfig
	66,  // 72: OCIRegistryResponse.PushResult:type_name -> OCIRegistryPushResponse
	2,   // 73: ApplicationService.ListApplications:input_type -> AppListRequest
	2,   // 74: ApplicationService.ListFluxApplications:input_type -> AppListRequest
	15,  // 75: ApplicationService.GetAppDetail:input_type -> AppDetailRequest
	15,  // 76: ApplicationService.GetAppStatus:input_type -> AppDetailRequest
	15,  // 77: ApplicationService.GetAppStatusV2:input_type -> AppDetailRequest
	28,  // 78: ApplicationService.Hibernate:input_type -> HibernateRequest
	28,  // 79: ApplicationService.UnHibernate:input_type -> HibernateRequest
	15,  // 80: ApplicationService.GetDeploymentHistory:input_type -> AppDetailRequest
	15,  // 81: ApplicationService.GetValuesYaml:input_type -> AppDetailRequest
	35,  // 82: ApplicationService.GetDesiredManifest:input_type -> ObjectRequest
	38,  // 83: ApplicationService.UninstallRelease:input_type -> ReleaseIdentifier
	39,  // 84: ApplicationService.UpgradeRelease:input_type -> UpgradeReleaseRequest
	41,  // 85: ApplicationService.GetDeploymentDetail:input_type -> DeploymentDetailRequest
	44,  // 86: ApplicationService.InstallRelease:input_type -> InstallReleaseRequest
	44,  // 87: ApplicationService.UpgradeReleaseWithChartInfo:input_type -> InstallReleaseRequest
	38,  // 88: ApplicationService.IsReleaseInstalled:input_type -> ReleaseIdentifier
	48,  // 89: ApplicationService.RollbackRelease:input_type -> RollbackReleaseRequest
	44,  // 90: ApplicationService.TemplateChart:input_type -> InstallReleaseRequest
	45,  // 91: ApplicationService.TemplateChartBulk:input_type -> BulkInstallReleaseRequest
	44,  // 92: ApplicationService.TemplateChartAndRetrieveChart:input_type -> InstallReleaseRequest
	52,  // 93: ApplicationService.InstallReleaseWithCustomChart:input_type -> HelmInstallCustomRequest
	44,  // 94: ApplicationService.GetNotes:input_type -> InstallReleaseRequest
	39,  // 95: ApplicationService.UpgradeReleaseWithCustomChart:input_type -> UpgradeReleaseRequest
	61,  // 96: ApplicationService.ValidateOCIRegistry:input_type -> RegistryCredential
	60,  // 97: ApplicationService.PushHelmChartToOCIRegistry:input_type -> OCIRegistryRequest
	5,   // 98: ApplicationService.GetResourceTreeForExternalResources:input_type -> ExternalResourceTreeRequest
	10,  // 99: ApplicationService.GetFluxAppDetail:input_type -> FluxAppDetailRequest
	38,  // 100: ApplicationService.GetReleaseDetails:input_type -> ReleaseIdentifier
	3,   // 101: ApplicationService.BuildResourceTreeUsingParentObjects:input_type -> GetResourceTreeRequest
	7,   // 102: ApplicationService.ListApplications:output_type -> DeployedAppList
	8,   // 103: ApplicationService.ListFluxApplications:output_type -> FluxApplicationList
	16,  // 104: ApplicationService.GetAppDetail:output_type -> AppDetail
	17,  // 105: ApplicationService.GetAppStatus:output_type -> AppStatus
	17,  // 106: ApplicationService.GetAppStatusV2:output_type -> AppStatus
	31,  // 107: ApplicationService.Hibernate:output_type -> HibernateResponse
	31,  // 108: ApplicationService.UnHibernate:output_type -> HibernateResponse
	33,  // 109: ApplicationService.GetDeploymentHistory:output_type -> HelmAppDeploymentHistory
	34,  // 110: ApplicationService.GetValuesYaml:output_type -> ReleaseInfo
	36,  // 111: ApplicationService.GetDesiredManifest:output_type -> DesiredManifestResponse
	37,  // 112: ApplicationService.UninstallRelease:output_type -> UninstallReleaseResponse
	40,  // 113: ApplicationService.UpgradeRelease:output_type -> UpgradeReleaseResponse
	42,  // 114: ApplicationService.GetDeploymentDetail:output_type -> DeploymentDetailResponse
	46,  // 115: ApplicationService.InstallRelease:output_type -> InstallReleaseResponse
	40,  // 116: ApplicationService.UpgradeReleaseWithChartInfo:output_type -> UpgradeReleaseResponse
	47,  // 117: ApplicationService.IsReleaseInstalled:output_type -> BooleanResponse
	47,  // 118: ApplicationService.RollbackRelease:output_type -> BooleanResponse
	49,  // 119: ApplicationService.TemplateChart:output_type -> TemplateChartResponse
	50,  // 120: ApplicationService.TemplateChartBulk:output_type -> BulkTemplateChartResponse
	51,  // 121: ApplicationService.TemplateChartAndRetrieveChart:output_type -> TemplateChartResponseWithChart
	53,  // 122: ApplicationService.InstallReleaseWithCustomChart:output_type -> HelmInstallCustomResponse
	59,  // 123: ApplicationService.GetNotes:output_type -> ChartNotesResponse
	40,  // 124: ApplicationService.UpgradeReleaseWithCustomChart:output_type -> UpgradeReleaseResponse
	65,  // 125: ApplicationService.ValidateOCIRegistry:output_type -> OCIRegistryResponse
	65,  // 126: ApplicationService.PushHelmChartToOCIRegistry:output_type -> OCIRegistryResponse
	20,  // 127: ApplicationService.GetResourceTreeForExternalResources:output_type -> ResourceTreeResponse
	11,  // 128: ApplicationService.GetFluxAppDetail:output_type -> FluxAppDetail
	13,  // 129: ApplicationService.GetReleaseDetails:output_type -> DeployedAppDetail
	20,  // 130: ApplicationService.BuildResourceTreeUsingParentObjects:output_type -> ResourceTreeResponse
	102, // [102:131] is the sub-list for method output_type
	73,  // [73:102] is the sub-list for method input_type
	73,  // [73:73] is the sub-list for extension type_name
	73,  // [73:73] is the sub-list for extension extendee
	0,   // [0:73] is the sub-list for field type_name
}

func init() { file_grpc_applist_proto_init() }
func file_grpc_applist_proto_init() {
	if File_grpc_applist_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_grpc_applist_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClusterConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetResourceTreeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CacheConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExternalResourceTreeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExternalResourceDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeployedAppList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FluxApplicationList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FluxApplication); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FluxAppDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FluxAppDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FluxAppStatusDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeployedAppDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnvironmentDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReleaseStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChartMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceTreeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InfoItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HealthStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceNetworkingInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceRef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PodMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EphemeralContainerData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HibernateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ObjectIdentifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HibernateStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HibernateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelmAppDeploymentDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelmAppDeploymentHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReleaseInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ObjectRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DesiredManifestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UninstallReleaseResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReleaseIdentifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpgradeReleaseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpgradeReleaseResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeploymentDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeploymentDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChartRepository); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InstallReleaseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulkInstallReleaseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InstallReleaseResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BooleanResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RollbackReleaseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemplateChartResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulkTemplateChartResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemplateChartResponseWithChart); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelmInstallCustomRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelmInstallCustomResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChartContent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Gvk); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceIdentifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceTreeFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChartNotesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OCIRegistryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegistryCredential); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProxyConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SSHTunnelConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoteConnectionConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OCIRegistryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_applist_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OCIRegistryPushResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_grpc_applist_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   69,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_grpc_applist_proto_goTypes,
		DependencyIndexes: file_grpc_applist_proto_depIdxs,
		EnumInfos:         file_grpc_applist_proto_enumTypes,
		MessageInfos:      file_grpc_applist_proto_msgTypes,
	}.Build()
	File_grpc_applist_proto = out.File
	file_grpc_applist_proto_rawDesc = nil
	file_grpc_applist_proto_goTypes = nil
	file_grpc_applist_proto_depIdxs = nil
}
