// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.9.1
// source: grpc/applist.proto

package client

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ApplicationService_ListApplications_FullMethodName                    = "/ApplicationService/ListApplications"
	ApplicationService_ListFluxApplications_FullMethodName                = "/ApplicationService/ListFluxApplications"
	ApplicationService_GetAppDetail_FullMethodName                        = "/ApplicationService/GetAppDetail"
	ApplicationService_GetAppStatus_FullMethodName                        = "/ApplicationService/GetAppStatus"
	ApplicationService_GetAppStatusV2_FullMethodName                      = "/ApplicationService/GetAppStatusV2"
	ApplicationService_Hibernate_FullMethodName                           = "/ApplicationService/Hibernate"
	ApplicationService_UnHibernate_FullMethodName                         = "/ApplicationService/UnHibernate"
	ApplicationService_GetDeploymentHistory_FullMethodName                = "/ApplicationService/GetDeploymentHistory"
	ApplicationService_GetValuesYaml_FullMethodName                       = "/ApplicationService/GetValuesYaml"
	ApplicationService_GetDesiredManifest_FullMethodName                  = "/ApplicationService/GetDesiredManifest"
	ApplicationService_UninstallRelease_FullMethodName                    = "/ApplicationService/UninstallRelease"
	ApplicationService_UpgradeRelease_FullMethodName                      = "/ApplicationService/UpgradeRelease"
	ApplicationService_GetDeploymentDetail_FullMethodName                 = "/ApplicationService/GetDeploymentDetail"
	ApplicationService_InstallRelease_FullMethodName                      = "/ApplicationService/InstallRelease"
	ApplicationService_UpgradeReleaseWithChartInfo_FullMethodName         = "/ApplicationService/UpgradeReleaseWithChartInfo"
	ApplicationService_IsReleaseInstalled_FullMethodName                  = "/ApplicationService/IsReleaseInstalled"
	ApplicationService_RollbackRelease_FullMethodName                     = "/ApplicationService/RollbackRelease"
	ApplicationService_TemplateChart_FullMethodName                       = "/ApplicationService/TemplateChart"
	ApplicationService_TemplateChartBulk_FullMethodName                   = "/ApplicationService/TemplateChartBulk"
	ApplicationService_TemplateChartAndRetrieveChart_FullMethodName       = "/ApplicationService/TemplateChartAndRetrieveChart"
	ApplicationService_InstallReleaseWithCustomChart_FullMethodName       = "/ApplicationService/InstallReleaseWithCustomChart"
	ApplicationService_GetNotes_FullMethodName                            = "/ApplicationService/GetNotes"
	ApplicationService_UpgradeReleaseWithCustomChart_FullMethodName       = "/ApplicationService/UpgradeReleaseWithCustomChart"
	ApplicationService_ValidateOCIRegistry_FullMethodName                 = "/ApplicationService/ValidateOCIRegistry"
	ApplicationService_PushHelmChartToOCIRegistry_FullMethodName          = "/ApplicationService/PushHelmChartToOCIRegistry"
	ApplicationService_GetResourceTreeForExternalResources_FullMethodName = "/ApplicationService/GetResourceTreeForExternalResources"
	ApplicationService_GetFluxAppDetail_FullMethodName                    = "/ApplicationService/GetFluxAppDetail"
	ApplicationService_GetReleaseDetails_FullMethodName                   = "/ApplicationService/GetReleaseDetails"
	ApplicationService_BuildResourceTreeUsingParentObjects_FullMethodName = "/ApplicationService/BuildResourceTreeUsingParentObjects"
)

// ApplicationServiceClient is the client API for ApplicationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ApplicationServiceClient interface {
	ListApplications(ctx context.Context, in *AppListRequest, opts ...grpc.CallOption) (ApplicationService_ListApplicationsClient, error)
	ListFluxApplications(ctx context.Context, in *AppListRequest, opts ...grpc.CallOption) (ApplicationService_ListFluxApplicationsClient, error)
	GetAppDetail(ctx context.Context, in *AppDetailRequest, opts ...grpc.CallOption) (*AppDetail, error)
	GetAppStatus(ctx context.Context, in *AppDetailRequest, opts ...grpc.CallOption) (*AppStatus, error)
	GetAppStatusV2(ctx context.Context, in *AppDetailRequest, opts ...grpc.CallOption) (*AppStatus, error)
	Hibernate(ctx context.Context, in *HibernateRequest, opts ...grpc.CallOption) (*HibernateResponse, error)
	UnHibernate(ctx context.Context, in *HibernateRequest, opts ...grpc.CallOption) (*HibernateResponse, error)
	GetDeploymentHistory(ctx context.Context, in *AppDetailRequest, opts ...grpc.CallOption) (*HelmAppDeploymentHistory, error)
	GetValuesYaml(ctx context.Context, in *AppDetailRequest, opts ...grpc.CallOption) (*ReleaseInfo, error)
	GetDesiredManifest(ctx context.Context, in *ObjectRequest, opts ...grpc.CallOption) (*DesiredManifestResponse, error)
	UninstallRelease(ctx context.Context, in *ReleaseIdentifier, opts ...grpc.CallOption) (*UninstallReleaseResponse, error)
	UpgradeRelease(ctx context.Context, in *UpgradeReleaseRequest, opts ...grpc.CallOption) (*UpgradeReleaseResponse, error)
	GetDeploymentDetail(ctx context.Context, in *DeploymentDetailRequest, opts ...grpc.CallOption) (*DeploymentDetailResponse, error)
	InstallRelease(ctx context.Context, in *InstallReleaseRequest, opts ...grpc.CallOption) (*InstallReleaseResponse, error)
	UpgradeReleaseWithChartInfo(ctx context.Context, in *InstallReleaseRequest, opts ...grpc.CallOption) (*UpgradeReleaseResponse, error)
	IsReleaseInstalled(ctx context.Context, in *ReleaseIdentifier, opts ...grpc.CallOption) (*BooleanResponse, error)
	RollbackRelease(ctx context.Context, in *RollbackReleaseRequest, opts ...grpc.CallOption) (*BooleanResponse, error)
	TemplateChart(ctx context.Context, in *InstallReleaseRequest, opts ...grpc.CallOption) (*TemplateChartResponse, error)
	TemplateChartBulk(ctx context.Context, in *BulkInstallReleaseRequest, opts ...grpc.CallOption) (*BulkTemplateChartResponse, error)
	TemplateChartAndRetrieveChart(ctx context.Context, in *InstallReleaseRequest, opts ...grpc.CallOption) (*TemplateChartResponseWithChart, error)
	InstallReleaseWithCustomChart(ctx context.Context, in *HelmInstallCustomRequest, opts ...grpc.CallOption) (*HelmInstallCustomResponse, error)
	GetNotes(ctx context.Context, in *InstallReleaseRequest, opts ...grpc.CallOption) (*ChartNotesResponse, error)
	UpgradeReleaseWithCustomChart(ctx context.Context, in *UpgradeReleaseRequest, opts ...grpc.CallOption) (*UpgradeReleaseResponse, error)
	ValidateOCIRegistry(ctx context.Context, in *RegistryCredential, opts ...grpc.CallOption) (*OCIRegistryResponse, error)
	PushHelmChartToOCIRegistry(ctx context.Context, in *OCIRegistryRequest, opts ...grpc.CallOption) (*OCIRegistryResponse, error)
	GetResourceTreeForExternalResources(ctx context.Context, in *ExternalResourceTreeRequest, opts ...grpc.CallOption) (*ResourceTreeResponse, error)
	GetFluxAppDetail(ctx context.Context, in *FluxAppDetailRequest, opts ...grpc.CallOption) (*FluxAppDetail, error)
	GetReleaseDetails(ctx context.Context, in *ReleaseIdentifier, opts ...grpc.CallOption) (*DeployedAppDetail, error)
	BuildResourceTreeUsingParentObjects(ctx context.Context, in *GetResourceTreeRequest, opts ...grpc.CallOption) (*ResourceTreeResponse, error)
}

type applicationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewApplicationServiceClient(cc grpc.ClientConnInterface) ApplicationServiceClient {
	return &applicationServiceClient{cc}
}

func (c *applicationServiceClient) ListApplications(ctx context.Context, in *AppListRequest, opts ...grpc.CallOption) (ApplicationService_ListApplicationsClient, error) {
	stream, err := c.cc.NewStream(ctx, &ApplicationService_ServiceDesc.Streams[0], ApplicationService_ListApplications_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &applicationServiceListApplicationsClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type ApplicationService_ListApplicationsClient interface {
	Recv() (*DeployedAppList, error)
	grpc.ClientStream
}

type applicationServiceListApplicationsClient struct {
	grpc.ClientStream
}

func (x *applicationServiceListApplicationsClient) Recv() (*DeployedAppList, error) {
	m := new(DeployedAppList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *applicationServiceClient) ListFluxApplications(ctx context.Context, in *AppListRequest, opts ...grpc.CallOption) (ApplicationService_ListFluxApplicationsClient, error) {
	stream, err := c.cc.NewStream(ctx, &ApplicationService_ServiceDesc.Streams[1], ApplicationService_ListFluxApplications_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &applicationServiceListFluxApplicationsClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type ApplicationService_ListFluxApplicationsClient interface {
	Recv() (*FluxApplicationList, error)
	grpc.ClientStream
}

type applicationServiceListFluxApplicationsClient struct {
	grpc.ClientStream
}

func (x *applicationServiceListFluxApplicationsClient) Recv() (*FluxApplicationList, error) {
	m := new(FluxApplicationList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *applicationServiceClient) GetAppDetail(ctx context.Context, in *AppDetailRequest, opts ...grpc.CallOption) (*AppDetail, error) {
	out := new(AppDetail)
	err := c.cc.Invoke(ctx, ApplicationService_GetAppDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) GetAppStatus(ctx context.Context, in *AppDetailRequest, opts ...grpc.CallOption) (*AppStatus, error) {
	out := new(AppStatus)
	err := c.cc.Invoke(ctx, ApplicationService_GetAppStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) GetAppStatusV2(ctx context.Context, in *AppDetailRequest, opts ...grpc.CallOption) (*AppStatus, error) {
	out := new(AppStatus)
	err := c.cc.Invoke(ctx, ApplicationService_GetAppStatusV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) Hibernate(ctx context.Context, in *HibernateRequest, opts ...grpc.CallOption) (*HibernateResponse, error) {
	out := new(HibernateResponse)
	err := c.cc.Invoke(ctx, ApplicationService_Hibernate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) UnHibernate(ctx context.Context, in *HibernateRequest, opts ...grpc.CallOption) (*HibernateResponse, error) {
	out := new(HibernateResponse)
	err := c.cc.Invoke(ctx, ApplicationService_UnHibernate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) GetDeploymentHistory(ctx context.Context, in *AppDetailRequest, opts ...grpc.CallOption) (*HelmAppDeploymentHistory, error) {
	out := new(HelmAppDeploymentHistory)
	err := c.cc.Invoke(ctx, ApplicationService_GetDeploymentHistory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) GetValuesYaml(ctx context.Context, in *AppDetailRequest, opts ...grpc.CallOption) (*ReleaseInfo, error) {
	out := new(ReleaseInfo)
	err := c.cc.Invoke(ctx, ApplicationService_GetValuesYaml_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) GetDesiredManifest(ctx context.Context, in *ObjectRequest, opts ...grpc.CallOption) (*DesiredManifestResponse, error) {
	out := new(DesiredManifestResponse)
	err := c.cc.Invoke(ctx, ApplicationService_GetDesiredManifest_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) UninstallRelease(ctx context.Context, in *ReleaseIdentifier, opts ...grpc.CallOption) (*UninstallReleaseResponse, error) {
	out := new(UninstallReleaseResponse)
	err := c.cc.Invoke(ctx, ApplicationService_UninstallRelease_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) UpgradeRelease(ctx context.Context, in *UpgradeReleaseRequest, opts ...grpc.CallOption) (*UpgradeReleaseResponse, error) {
	out := new(UpgradeReleaseResponse)
	err := c.cc.Invoke(ctx, ApplicationService_UpgradeRelease_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) GetDeploymentDetail(ctx context.Context, in *DeploymentDetailRequest, opts ...grpc.CallOption) (*DeploymentDetailResponse, error) {
	out := new(DeploymentDetailResponse)
	err := c.cc.Invoke(ctx, ApplicationService_GetDeploymentDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) InstallRelease(ctx context.Context, in *InstallReleaseRequest, opts ...grpc.CallOption) (*InstallReleaseResponse, error) {
	out := new(InstallReleaseResponse)
	err := c.cc.Invoke(ctx, ApplicationService_InstallRelease_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) UpgradeReleaseWithChartInfo(ctx context.Context, in *InstallReleaseRequest, opts ...grpc.CallOption) (*UpgradeReleaseResponse, error) {
	out := new(UpgradeReleaseResponse)
	err := c.cc.Invoke(ctx, ApplicationService_UpgradeReleaseWithChartInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) IsReleaseInstalled(ctx context.Context, in *ReleaseIdentifier, opts ...grpc.CallOption) (*BooleanResponse, error) {
	out := new(BooleanResponse)
	err := c.cc.Invoke(ctx, ApplicationService_IsReleaseInstalled_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) RollbackRelease(ctx context.Context, in *RollbackReleaseRequest, opts ...grpc.CallOption) (*BooleanResponse, error) {
	out := new(BooleanResponse)
	err := c.cc.Invoke(ctx, ApplicationService_RollbackRelease_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) TemplateChart(ctx context.Context, in *InstallReleaseRequest, opts ...grpc.CallOption) (*TemplateChartResponse, error) {
	out := new(TemplateChartResponse)
	err := c.cc.Invoke(ctx, ApplicationService_TemplateChart_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) TemplateChartBulk(ctx context.Context, in *BulkInstallReleaseRequest, opts ...grpc.CallOption) (*BulkTemplateChartResponse, error) {
	out := new(BulkTemplateChartResponse)
	err := c.cc.Invoke(ctx, ApplicationService_TemplateChartBulk_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) TemplateChartAndRetrieveChart(ctx context.Context, in *InstallReleaseRequest, opts ...grpc.CallOption) (*TemplateChartResponseWithChart, error) {
	out := new(TemplateChartResponseWithChart)
	err := c.cc.Invoke(ctx, ApplicationService_TemplateChartAndRetrieveChart_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) InstallReleaseWithCustomChart(ctx context.Context, in *HelmInstallCustomRequest, opts ...grpc.CallOption) (*HelmInstallCustomResponse, error) {
	out := new(HelmInstallCustomResponse)
	err := c.cc.Invoke(ctx, ApplicationService_InstallReleaseWithCustomChart_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) GetNotes(ctx context.Context, in *InstallReleaseRequest, opts ...grpc.CallOption) (*ChartNotesResponse, error) {
	out := new(ChartNotesResponse)
	err := c.cc.Invoke(ctx, ApplicationService_GetNotes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) UpgradeReleaseWithCustomChart(ctx context.Context, in *UpgradeReleaseRequest, opts ...grpc.CallOption) (*UpgradeReleaseResponse, error) {
	out := new(UpgradeReleaseResponse)
	err := c.cc.Invoke(ctx, ApplicationService_UpgradeReleaseWithCustomChart_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) ValidateOCIRegistry(ctx context.Context, in *RegistryCredential, opts ...grpc.CallOption) (*OCIRegistryResponse, error) {
	out := new(OCIRegistryResponse)
	err := c.cc.Invoke(ctx, ApplicationService_ValidateOCIRegistry_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) PushHelmChartToOCIRegistry(ctx context.Context, in *OCIRegistryRequest, opts ...grpc.CallOption) (*OCIRegistryResponse, error) {
	out := new(OCIRegistryResponse)
	err := c.cc.Invoke(ctx, ApplicationService_PushHelmChartToOCIRegistry_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) GetResourceTreeForExternalResources(ctx context.Context, in *ExternalResourceTreeRequest, opts ...grpc.CallOption) (*ResourceTreeResponse, error) {
	out := new(ResourceTreeResponse)
	err := c.cc.Invoke(ctx, ApplicationService_GetResourceTreeForExternalResources_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) GetFluxAppDetail(ctx context.Context, in *FluxAppDetailRequest, opts ...grpc.CallOption) (*FluxAppDetail, error) {
	out := new(FluxAppDetail)
	err := c.cc.Invoke(ctx, ApplicationService_GetFluxAppDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) GetReleaseDetails(ctx context.Context, in *ReleaseIdentifier, opts ...grpc.CallOption) (*DeployedAppDetail, error) {
	out := new(DeployedAppDetail)
	err := c.cc.Invoke(ctx, ApplicationService_GetReleaseDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applicationServiceClient) BuildResourceTreeUsingParentObjects(ctx context.Context, in *GetResourceTreeRequest, opts ...grpc.CallOption) (*ResourceTreeResponse, error) {
	out := new(ResourceTreeResponse)
	err := c.cc.Invoke(ctx, ApplicationService_BuildResourceTreeUsingParentObjects_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ApplicationServiceServer is the server API for ApplicationService service.
// All implementations must embed UnimplementedApplicationServiceServer
// for forward compatibility
type ApplicationServiceServer interface {
	ListApplications(*AppListRequest, ApplicationService_ListApplicationsServer) error
	ListFluxApplications(*AppListRequest, ApplicationService_ListFluxApplicationsServer) error
	GetAppDetail(context.Context, *AppDetailRequest) (*AppDetail, error)
	GetAppStatus(context.Context, *AppDetailRequest) (*AppStatus, error)
	GetAppStatusV2(context.Context, *AppDetailRequest) (*AppStatus, error)
	Hibernate(context.Context, *HibernateRequest) (*HibernateResponse, error)
	UnHibernate(context.Context, *HibernateRequest) (*HibernateResponse, error)
	GetDeploymentHistory(context.Context, *AppDetailRequest) (*HelmAppDeploymentHistory, error)
	GetValuesYaml(context.Context, *AppDetailRequest) (*ReleaseInfo, error)
	GetDesiredManifest(context.Context, *ObjectRequest) (*DesiredManifestResponse, error)
	UninstallRelease(context.Context, *ReleaseIdentifier) (*UninstallReleaseResponse, error)
	UpgradeRelease(context.Context, *UpgradeReleaseRequest) (*UpgradeReleaseResponse, error)
	GetDeploymentDetail(context.Context, *DeploymentDetailRequest) (*DeploymentDetailResponse, error)
	InstallRelease(context.Context, *InstallReleaseRequest) (*InstallReleaseResponse, error)
	UpgradeReleaseWithChartInfo(context.Context, *InstallReleaseRequest) (*UpgradeReleaseResponse, error)
	IsReleaseInstalled(context.Context, *ReleaseIdentifier) (*BooleanResponse, error)
	RollbackRelease(context.Context, *RollbackReleaseRequest) (*BooleanResponse, error)
	TemplateChart(context.Context, *InstallReleaseRequest) (*TemplateChartResponse, error)
	TemplateChartBulk(context.Context, *BulkInstallReleaseRequest) (*BulkTemplateChartResponse, error)
	TemplateChartAndRetrieveChart(context.Context, *InstallReleaseRequest) (*TemplateChartResponseWithChart, error)
	InstallReleaseWithCustomChart(context.Context, *HelmInstallCustomRequest) (*HelmInstallCustomResponse, error)
	GetNotes(context.Context, *InstallReleaseRequest) (*ChartNotesResponse, error)
	UpgradeReleaseWithCustomChart(context.Context, *UpgradeReleaseRequest) (*UpgradeReleaseResponse, error)
	ValidateOCIRegistry(context.Context, *RegistryCredential) (*OCIRegistryResponse, error)
	PushHelmChartToOCIRegistry(context.Context, *OCIRegistryRequest) (*OCIRegistryResponse, error)
	GetResourceTreeForExternalResources(context.Context, *ExternalResourceTreeRequest) (*ResourceTreeResponse, error)
	GetFluxAppDetail(context.Context, *FluxAppDetailRequest) (*FluxAppDetail, error)
	GetReleaseDetails(context.Context, *ReleaseIdentifier) (*DeployedAppDetail, error)
	BuildResourceTreeUsingParentObjects(context.Context, *GetResourceTreeRequest) (*ResourceTreeResponse, error)
	mustEmbedUnimplementedApplicationServiceServer()
}

// UnimplementedApplicationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedApplicationServiceServer struct {
}

func (UnimplementedApplicationServiceServer) ListApplications(*AppListRequest, ApplicationService_ListApplicationsServer) error {
	return status.Errorf(codes.Unimplemented, "method ListApplications not implemented")
}
func (UnimplementedApplicationServiceServer) ListFluxApplications(*AppListRequest, ApplicationService_ListFluxApplicationsServer) error {
	return status.Errorf(codes.Unimplemented, "method ListFluxApplications not implemented")
}
func (UnimplementedApplicationServiceServer) GetAppDetail(context.Context, *AppDetailRequest) (*AppDetail, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppDetail not implemented")
}
func (UnimplementedApplicationServiceServer) GetAppStatus(context.Context, *AppDetailRequest) (*AppStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppStatus not implemented")
}
func (UnimplementedApplicationServiceServer) GetAppStatusV2(context.Context, *AppDetailRequest) (*AppStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppStatusV2 not implemented")
}
func (UnimplementedApplicationServiceServer) Hibernate(context.Context, *HibernateRequest) (*HibernateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Hibernate not implemented")
}
func (UnimplementedApplicationServiceServer) UnHibernate(context.Context, *HibernateRequest) (*HibernateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnHibernate not implemented")
}
func (UnimplementedApplicationServiceServer) GetDeploymentHistory(context.Context, *AppDetailRequest) (*HelmAppDeploymentHistory, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeploymentHistory not implemented")
}
func (UnimplementedApplicationServiceServer) GetValuesYaml(context.Context, *AppDetailRequest) (*ReleaseInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetValuesYaml not implemented")
}
func (UnimplementedApplicationServiceServer) GetDesiredManifest(context.Context, *ObjectRequest) (*DesiredManifestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDesiredManifest not implemented")
}
func (UnimplementedApplicationServiceServer) UninstallRelease(context.Context, *ReleaseIdentifier) (*UninstallReleaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UninstallRelease not implemented")
}
func (UnimplementedApplicationServiceServer) UpgradeRelease(context.Context, *UpgradeReleaseRequest) (*UpgradeReleaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpgradeRelease not implemented")
}
func (UnimplementedApplicationServiceServer) GetDeploymentDetail(context.Context, *DeploymentDetailRequest) (*DeploymentDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeploymentDetail not implemented")
}
func (UnimplementedApplicationServiceServer) InstallRelease(context.Context, *InstallReleaseRequest) (*InstallReleaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InstallRelease not implemented")
}
func (UnimplementedApplicationServiceServer) UpgradeReleaseWithChartInfo(context.Context, *InstallReleaseRequest) (*UpgradeReleaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpgradeReleaseWithChartInfo not implemented")
}
func (UnimplementedApplicationServiceServer) IsReleaseInstalled(context.Context, *ReleaseIdentifier) (*BooleanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsReleaseInstalled not implemented")
}
func (UnimplementedApplicationServiceServer) RollbackRelease(context.Context, *RollbackReleaseRequest) (*BooleanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RollbackRelease not implemented")
}
func (UnimplementedApplicationServiceServer) TemplateChart(context.Context, *InstallReleaseRequest) (*TemplateChartResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TemplateChart not implemented")
}
func (UnimplementedApplicationServiceServer) TemplateChartBulk(context.Context, *BulkInstallReleaseRequest) (*BulkTemplateChartResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TemplateChartBulk not implemented")
}
func (UnimplementedApplicationServiceServer) TemplateChartAndRetrieveChart(context.Context, *InstallReleaseRequest) (*TemplateChartResponseWithChart, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TemplateChartAndRetrieveChart not implemented")
}
func (UnimplementedApplicationServiceServer) InstallReleaseWithCustomChart(context.Context, *HelmInstallCustomRequest) (*HelmInstallCustomResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InstallReleaseWithCustomChart not implemented")
}
func (UnimplementedApplicationServiceServer) GetNotes(context.Context, *InstallReleaseRequest) (*ChartNotesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNotes not implemented")
}
func (UnimplementedApplicationServiceServer) UpgradeReleaseWithCustomChart(context.Context, *UpgradeReleaseRequest) (*UpgradeReleaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpgradeReleaseWithCustomChart not implemented")
}
func (UnimplementedApplicationServiceServer) ValidateOCIRegistry(context.Context, *RegistryCredential) (*OCIRegistryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateOCIRegistry not implemented")
}
func (UnimplementedApplicationServiceServer) PushHelmChartToOCIRegistry(context.Context, *OCIRegistryRequest) (*OCIRegistryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushHelmChartToOCIRegistry not implemented")
}
func (UnimplementedApplicationServiceServer) GetResourceTreeForExternalResources(context.Context, *ExternalResourceTreeRequest) (*ResourceTreeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetResourceTreeForExternalResources not implemented")
}
func (UnimplementedApplicationServiceServer) GetFluxAppDetail(context.Context, *FluxAppDetailRequest) (*FluxAppDetail, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFluxAppDetail not implemented")
}
func (UnimplementedApplicationServiceServer) GetReleaseDetails(context.Context, *ReleaseIdentifier) (*DeployedAppDetail, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReleaseDetails not implemented")
}
func (UnimplementedApplicationServiceServer) BuildResourceTreeUsingParentObjects(context.Context, *GetResourceTreeRequest) (*ResourceTreeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildResourceTreeUsingParentObjects not implemented")
}
func (UnimplementedApplicationServiceServer) mustEmbedUnimplementedApplicationServiceServer() {}

// UnsafeApplicationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ApplicationServiceServer will
// result in compilation errors.
type UnsafeApplicationServiceServer interface {
	mustEmbedUnimplementedApplicationServiceServer()
}

func RegisterApplicationServiceServer(s grpc.ServiceRegistrar, srv ApplicationServiceServer) {
	s.RegisterService(&ApplicationService_ServiceDesc, srv)
}

func _ApplicationService_ListApplications_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(AppListRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ApplicationServiceServer).ListApplications(m, &applicationServiceListApplicationsServer{stream})
}

type ApplicationService_ListApplicationsServer interface {
	Send(*DeployedAppList) error
	grpc.ServerStream
}

type applicationServiceListApplicationsServer struct {
	grpc.ServerStream
}

func (x *applicationServiceListApplicationsServer) Send(m *DeployedAppList) error {
	return x.ServerStream.SendMsg(m)
}

func _ApplicationService_ListFluxApplications_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(AppListRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ApplicationServiceServer).ListFluxApplications(m, &applicationServiceListFluxApplicationsServer{stream})
}

type ApplicationService_ListFluxApplicationsServer interface {
	Send(*FluxApplicationList) error
	grpc.ServerStream
}

type applicationServiceListFluxApplicationsServer struct {
	grpc.ServerStream
}

func (x *applicationServiceListFluxApplicationsServer) Send(m *FluxApplicationList) error {
	return x.ServerStream.SendMsg(m)
}

func _ApplicationService_GetAppDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AppDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).GetAppDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_GetAppDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).GetAppDetail(ctx, req.(*AppDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_GetAppStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AppDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).GetAppStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_GetAppStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).GetAppStatus(ctx, req.(*AppDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_GetAppStatusV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AppDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).GetAppStatusV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_GetAppStatusV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).GetAppStatusV2(ctx, req.(*AppDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_Hibernate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HibernateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).Hibernate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_Hibernate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).Hibernate(ctx, req.(*HibernateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_UnHibernate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HibernateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).UnHibernate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_UnHibernate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).UnHibernate(ctx, req.(*HibernateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_GetDeploymentHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AppDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).GetDeploymentHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_GetDeploymentHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).GetDeploymentHistory(ctx, req.(*AppDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_GetValuesYaml_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AppDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).GetValuesYaml(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_GetValuesYaml_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).GetValuesYaml(ctx, req.(*AppDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_GetDesiredManifest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ObjectRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).GetDesiredManifest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_GetDesiredManifest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).GetDesiredManifest(ctx, req.(*ObjectRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_UninstallRelease_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReleaseIdentifier)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).UninstallRelease(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_UninstallRelease_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).UninstallRelease(ctx, req.(*ReleaseIdentifier))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_UpgradeRelease_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpgradeReleaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).UpgradeRelease(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_UpgradeRelease_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).UpgradeRelease(ctx, req.(*UpgradeReleaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_GetDeploymentDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeploymentDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).GetDeploymentDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_GetDeploymentDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).GetDeploymentDetail(ctx, req.(*DeploymentDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_InstallRelease_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InstallReleaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).InstallRelease(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_InstallRelease_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).InstallRelease(ctx, req.(*InstallReleaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_UpgradeReleaseWithChartInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InstallReleaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).UpgradeReleaseWithChartInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_UpgradeReleaseWithChartInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).UpgradeReleaseWithChartInfo(ctx, req.(*InstallReleaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_IsReleaseInstalled_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReleaseIdentifier)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).IsReleaseInstalled(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_IsReleaseInstalled_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).IsReleaseInstalled(ctx, req.(*ReleaseIdentifier))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_RollbackRelease_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RollbackReleaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).RollbackRelease(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_RollbackRelease_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).RollbackRelease(ctx, req.(*RollbackReleaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_TemplateChart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InstallReleaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).TemplateChart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_TemplateChart_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).TemplateChart(ctx, req.(*InstallReleaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_TemplateChartBulk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BulkInstallReleaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).TemplateChartBulk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_TemplateChartBulk_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).TemplateChartBulk(ctx, req.(*BulkInstallReleaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_TemplateChartAndRetrieveChart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InstallReleaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).TemplateChartAndRetrieveChart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_TemplateChartAndRetrieveChart_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).TemplateChartAndRetrieveChart(ctx, req.(*InstallReleaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_InstallReleaseWithCustomChart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HelmInstallCustomRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).InstallReleaseWithCustomChart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_InstallReleaseWithCustomChart_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).InstallReleaseWithCustomChart(ctx, req.(*HelmInstallCustomRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_GetNotes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InstallReleaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).GetNotes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_GetNotes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).GetNotes(ctx, req.(*InstallReleaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_UpgradeReleaseWithCustomChart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpgradeReleaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).UpgradeReleaseWithCustomChart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_UpgradeReleaseWithCustomChart_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).UpgradeReleaseWithCustomChart(ctx, req.(*UpgradeReleaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_ValidateOCIRegistry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegistryCredential)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).ValidateOCIRegistry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_ValidateOCIRegistry_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).ValidateOCIRegistry(ctx, req.(*RegistryCredential))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_PushHelmChartToOCIRegistry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OCIRegistryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).PushHelmChartToOCIRegistry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_PushHelmChartToOCIRegistry_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).PushHelmChartToOCIRegistry(ctx, req.(*OCIRegistryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_GetResourceTreeForExternalResources_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExternalResourceTreeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).GetResourceTreeForExternalResources(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_GetResourceTreeForExternalResources_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).GetResourceTreeForExternalResources(ctx, req.(*ExternalResourceTreeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_GetFluxAppDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FluxAppDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).GetFluxAppDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_GetFluxAppDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).GetFluxAppDetail(ctx, req.(*FluxAppDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_GetReleaseDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReleaseIdentifier)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).GetReleaseDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_GetReleaseDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).GetReleaseDetails(ctx, req.(*ReleaseIdentifier))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplicationService_BuildResourceTreeUsingParentObjects_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetResourceTreeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplicationServiceServer).BuildResourceTreeUsingParentObjects(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ApplicationService_BuildResourceTreeUsingParentObjects_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplicationServiceServer).BuildResourceTreeUsingParentObjects(ctx, req.(*GetResourceTreeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ApplicationService_ServiceDesc is the grpc.ServiceDesc for ApplicationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ApplicationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ApplicationService",
	HandlerType: (*ApplicationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAppDetail",
			Handler:    _ApplicationService_GetAppDetail_Handler,
		},
		{
			MethodName: "GetAppStatus",
			Handler:    _ApplicationService_GetAppStatus_Handler,
		},
		{
			MethodName: "GetAppStatusV2",
			Handler:    _ApplicationService_GetAppStatusV2_Handler,
		},
		{
			MethodName: "Hibernate",
			Handler:    _ApplicationService_Hibernate_Handler,
		},
		{
			MethodName: "UnHibernate",
			Handler:    _ApplicationService_UnHibernate_Handler,
		},
		{
			MethodName: "GetDeploymentHistory",
			Handler:    _ApplicationService_GetDeploymentHistory_Handler,
		},
		{
			MethodName: "GetValuesYaml",
			Handler:    _ApplicationService_GetValuesYaml_Handler,
		},
		{
			MethodName: "GetDesiredManifest",
			Handler:    _ApplicationService_GetDesiredManifest_Handler,
		},
		{
			MethodName: "UninstallRelease",
			Handler:    _ApplicationService_UninstallRelease_Handler,
		},
		{
			MethodName: "UpgradeRelease",
			Handler:    _ApplicationService_UpgradeRelease_Handler,
		},
		{
			MethodName: "GetDeploymentDetail",
			Handler:    _ApplicationService_GetDeploymentDetail_Handler,
		},
		{
			MethodName: "InstallRelease",
			Handler:    _ApplicationService_InstallRelease_Handler,
		},
		{
			MethodName: "UpgradeReleaseWithChartInfo",
			Handler:    _ApplicationService_UpgradeReleaseWithChartInfo_Handler,
		},
		{
			MethodName: "IsReleaseInstalled",
			Handler:    _ApplicationService_IsReleaseInstalled_Handler,
		},
		{
			MethodName: "RollbackRelease",
			Handler:    _ApplicationService_RollbackRelease_Handler,
		},
		{
			MethodName: "TemplateChart",
			Handler:    _ApplicationService_TemplateChart_Handler,
		},
		{
			MethodName: "TemplateChartBulk",
			Handler:    _ApplicationService_TemplateChartBulk_Handler,
		},
		{
			MethodName: "TemplateChartAndRetrieveChart",
			Handler:    _ApplicationService_TemplateChartAndRetrieveChart_Handler,
		},
		{
			MethodName: "InstallReleaseWithCustomChart",
			Handler:    _ApplicationService_InstallReleaseWithCustomChart_Handler,
		},
		{
			MethodName: "GetNotes",
			Handler:    _ApplicationService_GetNotes_Handler,
		},
		{
			MethodName: "UpgradeReleaseWithCustomChart",
			Handler:    _ApplicationService_UpgradeReleaseWithCustomChart_Handler,
		},
		{
			MethodName: "ValidateOCIRegistry",
			Handler:    _ApplicationService_ValidateOCIRegistry_Handler,
		},
		{
			MethodName: "PushHelmChartToOCIRegistry",
			Handler:    _ApplicationService_PushHelmChartToOCIRegistry_Handler,
		},
		{
			MethodName: "GetResourceTreeForExternalResources",
			Handler:    _ApplicationService_GetResourceTreeForExternalResources_Handler,
		},
		{
			MethodName: "GetFluxAppDetail",
			Handler:    _ApplicationService_GetFluxAppDetail_Handler,
		},
		{
			MethodName: "GetReleaseDetails",
			Handler:    _ApplicationService_GetReleaseDetails_Handler,
		},
		{
			MethodName: "BuildResourceTreeUsingParentObjects",
			Handler:    _ApplicationService_BuildResourceTreeUsingParentObjects_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "ListApplications",
			Handler:       _ApplicationService_ListApplications_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "ListFluxApplications",
			Handler:       _ApplicationService_ListFluxApplications_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "grpc/applist.proto",
}
