[{"Category": "DEVTRON", "Fields": [{"Env": "APP", "EnvType": "string", "EnvValue": "kubelink", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "CONSUMER_CONFIG_JSON", "EnvType": "string", "EnvValue": "", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "DEFAULT_LOG_TIME_LIMIT", "EnvType": "int64", "EnvValue": "1", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "ENABLE_STATSVIZ", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_CLIENT_MAX_IDLE_CONNS_PER_HOST", "EnvType": "int", "EnvValue": "25", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_TCP_IDLE_CONN_TIMEOUT", "EnvType": "int", "EnvValue": "300", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_TCP_KEEPALIVE", "EnvType": "int", "EnvValue": "30", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_TCP_TIMEOUT", "EnvType": "int", "EnvValue": "30", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_TLS_HANDSHAKE_TIMEOUT", "EnvType": "int", "EnvValue": "10", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "LOG_LEVEL", "EnvType": "int", "EnvValue": "-1", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NATS_MSG_ACK_WAIT_IN_SECS", "EnvType": "int", "EnvValue": "120", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NATS_MSG_BUFFER_SIZE", "EnvType": "int", "EnvValue": "-1", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NATS_MSG_MAX_AGE", "EnvType": "int", "EnvValue": "86400", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NATS_MSG_PROCESSING_BATCH_SIZE", "EnvType": "int", "EnvValue": "1", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NATS_MSG_REPLICAS", "EnvType": "int", "EnvValue": "0", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NATS_SERVER_HOST", "EnvType": "string", "EnvValue": "nats://devtron-nats.devtroncd:4222", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_ADDR", "EnvType": "string", "EnvValue": "127.0.0.1", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_DATABASE", "EnvType": "string", "EnvValue": "orchestrator", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_EXPORT_PROM_METRICS", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_LOG_ALL_FAILURE_QUERIES", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_LOG_ALL_QUERY", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_LOG_SLOW_QUERY", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_PASSWORD", "EnvType": "string", "EnvValue": "", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_PORT", "EnvType": "string", "EnvValue": "5432", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_QUERY_DUR_THRESHOLD", "EnvType": "int64", "EnvValue": "5000", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_USER", "EnvType": "string", "EnvValue": "", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "RUNTIME_CONFIG_LOCAL_DEV", "EnvType": "LocalDevMode", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "STREAM_CONFIG_JSON", "EnvType": "string", "EnvValue": "", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "USE_CUSTOM_HTTP_TRANSPORT", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}]}, {"Category": "HELM_RELEASE", "Fields": [{"Env": "BUILD_NODES_BATCH_SIZE", "EnvType": "int", "EnvValue": "2", "EnvDescription": "Resource tree build nodes parallelism batch size (applied only for depth-1 child objects of a parent object)", "Example": "2", "Deprecated": "false"}, {"Env": "CHART_WORKING_DIRECTORY", "EnvType": "string", "EnvValue": "/home/<USER>/devtroncd/charts/", "EnvDescription": "<PERSON><PERSON> charts working directory", "Example": "/home/<USER>/devtroncd/charts/", "Deprecated": "false"}, {"Env": "ENABLE_HELM_RELEASE_CACHE", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "Enable helm releases list cache", "Example": "true", "Deprecated": "false"}, {"Env": "FEAT_CHILD_OBJECT_LISTING_PAGINATION", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "use pagination in listing all the dependent child objects. use 'CHILD_OBJECT_LISTING_PAGE_SIZE' to set the page size.", "Example": "true", "Deprecated": "false"}, {"Env": "MANIFEST_FETCH_BATCH_SIZE", "EnvType": "int", "EnvValue": "2", "EnvDescription": "Manifest fetch parallelism batch size (applied only for parent objects)", "Example": "2", "Deprecated": "false"}, {"Env": "MAX_COUNT_FOR_HELM_RELEASE", "EnvType": "int", "EnvValue": "20", "EnvDescription": "Max count for helm release history list", "Example": "20", "Deprecated": "false"}, {"Env": "RUN_HELM_INSTALL_IN_ASYNC_MODE", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "Run helm install/ upgrade in async mode", "Example": "false", "Deprecated": "false"}]}, {"Category": "INFRA_SETUP", "Fields": [{"Env": "KUBELINK_GRPC_MAX_RECEIVE_MSG_SIZE", "EnvType": "int", "EnvValue": "20", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "KUBELINK_GRPC_MAX_SEND_MSG_SIZE", "EnvType": "int", "EnvValue": "4", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "KUBELINK_GRPC_SERVICE_CONFIG", "EnvType": "string", "EnvValue": "{\"loadBalancingPolicy\":\"round_robin\"}", "EnvDescription": "kubelink grpc service config", "Example": "", "Deprecated": "false"}]}, {"Category": "K8S_RESOURCE_SERVICE_CONFIG", "Fields": [{"Env": "CHILD_OBJECT_LISTING_PAGE_SIZE", "EnvType": "int64", "EnvValue": "1000", "EnvDescription": "Resource tree child object listing page size", "Example": "100", "Deprecated": "false"}, {"Env": "PARENT_CHILD_GVK_MAPPING", "EnvType": "string", "EnvValue": "", "EnvDescription": "Parent child GVK mapping for resource tree", "Example": "", "Deprecated": "false"}]}]